# pages/2_configure_display.py
import streamlit as st
import numpy as np
from common.constants import (
    AVAILABLE_OUTPUTS_ALL_MODES, AVAILABLE_OUTPUTS_SECTION, 
    GPU_BATCH_SIZES, CPU_FALLBACK_BATCH_SIZES, GPU_PROCESSING_MODES,
    DEFAULT_PROCESSING_MODE
)
from common.session_state import initialize_session_state, get_gpu_status
from utils.gpu_utils import get_optimal_processing_config, optimize_batch_size_for_mode, GPU_AVAILABLE
import logging

st.set_page_config(page_title="Configure Display", layout="wide")
st.title("Step 2: Configure Display & Processing (GPU-Optimized)")

# Initialize session state
initialize_session_state()

# Check for loaded data
if not st.session_state.get('header_loader'):
    st.error("❌ No data loaded. Please go to Step 1 to load your SEG-Y file.")
    st.stop()

# Display GPU status
gpu_status = get_gpu_status()
st.markdown("### 🚀 Processing Configuration Status")
col1, col2, col3 = st.columns(3)
with col1:
    if gpu_status['available']:
        st.success("✅ GPU Acceleration Available")
    else:
        st.warning("⚠️ CPU Processing Mode")
with col2:
    st.info(f"Backend: {gpu_status['backend']}")
with col3:
    st.info(f"Memory Mode: {gpu_status['memory_mode']}")

st.markdown("---")

# Initialize plot_settings if not exists or ensure it's a dictionary
if 'plot_settings' not in st.session_state or st.session_state.plot_settings is None:
    st.session_state.plot_settings = {}
elif not isinstance(st.session_state.plot_settings, dict):
    # Defensive programming: ensure plot_settings is always a dictionary
    st.session_state.plot_settings = {}

# Helper function to safely get plot settings values
def safe_get_plot_setting(key, default_value):
    """Safely get a value from plot_settings with fallback to default."""
    if st.session_state.plot_settings is None:
        st.session_state.plot_settings = {}
    return st.session_state.plot_settings.get(key, default_value)

# --- GPU Processing Configuration ---
st.markdown("### ⚙️ GPU Processing Configuration")

col1, col2 = st.columns(2)

with col1:
    # Processing mode selection
    processing_mode = st.selectbox(
        "Processing Mode:",
        list(GPU_PROCESSING_MODES.keys()),
        index=list(GPU_PROCESSING_MODES.keys()).index(st.session_state.get('processing_mode', DEFAULT_PROCESSING_MODE)),
        format_func=lambda x: GPU_PROCESSING_MODES[x],
        help="Choose GPU/CPU processing preference"
    )
    st.session_state.processing_mode = processing_mode
    
    # GPU Memory Mode (if GPU available)
    if gpu_status['available']:
        memory_modes = ["Conservative (60%)", "Balanced (80%)", "Aggressive (90%)"]
        current_mode = st.session_state.get('gpu_memory_mode', "Balanced (80%)")
        memory_mode = st.selectbox(
            "GPU Memory Usage:",
            memory_modes,
            index=memory_modes.index(current_mode) if current_mode in memory_modes else 1,
            help="GPU memory usage strategy for optimal performance"
        )
        st.session_state.gpu_memory_mode = memory_mode

with col2:
    # Real-time GPU optimization display
    if st.session_state.get('analysis_mode'):
        trace_count = st.session_state.get('trace_count', 1000)
        config = get_optimal_processing_config(st.session_state.analysis_mode, trace_count)
        
        st.markdown("#### 🎯 Current Optimization")
        st.metric("Recommended Batch Size", config['batch_size'])
        st.metric("Processing Backend", config['backend'])
        st.metric("Estimated Batches", config['estimated_batches'])
        
        # Memory impact assessment
        if gpu_status['available'] and st.session_state.get('gpu_device_info'):
            est_memory_gb = (config['batch_size'] * 1000 * 4) / (1024**3)
            st.metric("Est. Memory Usage", f"{est_memory_gb:.1f} GB")
    else:
        st.info("Select analysis area in Step 3 to see optimization details")

st.markdown("---")

# --- Output Selection ---
st.markdown("### 📊 Output Selection")

# Determine available outputs based on analysis mode
if st.session_state.get('analysis_mode') in ["By inline/crossline section (AOI)", "By Polyline File Import"]:
    available_outputs = AVAILABLE_OUTPUTS_SECTION
else:
    available_outputs = AVAILABLE_OUTPUTS_ALL_MODES

# Output selection with GPU batch optimization
selected_outputs = st.multiselect(
    "Select Spectral Descriptors to Calculate:",
    available_outputs,
    default=st.session_state.get('selected_outputs', ["Input Signal", "HFC", "Spectral Decrease"]),
    help="Choose outputs to calculate. More outputs require more GPU memory."
)
st.session_state.selected_outputs = selected_outputs

# GPU batch size optimization based on selected outputs
if selected_outputs and gpu_status['available']:
    output_count = len(selected_outputs)
    base_batch = GPU_BATCH_SIZES.get(st.session_state.get('analysis_mode', 'default'), 512)
    
    # Adjust batch size based on output complexity
    if output_count > 5:
        recommended_batch = max(base_batch // 2, 128)
        st.warning(f"⚠️ Many outputs selected. Recommended batch size reduced to {recommended_batch}")
    elif output_count > 8:
        recommended_batch = max(base_batch // 4, 64)
        st.error(f"🚨 Too many outputs may cause memory issues. Consider reducing selection.")
    else:
        recommended_batch = base_batch
        st.success(f"✅ Output selection optimized for GPU processing")

st.markdown("---")

# --- Spectral Processing Parameters ---
st.markdown("### 🔊 Spectral Processing Parameters")

col1, col2, col3 = st.columns(3)

with col1:
    st.markdown("#### Window Parameters")
    st.session_state.plot_settings['int_val'] = st.number_input(
        "Window Length (ms)",
        min_value=10.0,
        max_value=200.0,
        value=safe_get_plot_setting('int_val', 35.0),
        step=5.0,
        help="Time window length for spectral analysis"
    )

    st.session_state.plot_settings['b1'] = st.number_input(
        "Low Frequency (Hz)",
        min_value=1.0,
        max_value=100.0,
        value=safe_get_plot_setting('b1', 5.0),
        step=1.0,
        help="Lower frequency bound for analysis"
    )

    # Add shape parameter (from original implementation)
    st.session_state.plot_settings['shape'] = st.number_input(
        "Shape Parameter",
        min_value=0.0,
        max_value=2.0,
        value=safe_get_plot_setting('shape', 0.35),
        step=0.01,
        help="Shape parameter for spectral analysis"
    )

with col2:
    st.markdown("#### Frequency Parameters")
    st.session_state.plot_settings['b2'] = st.number_input(
        "High Frequency (Hz)",
        min_value=10.0,
        max_value=200.0,
        value=safe_get_plot_setting('b2', 40.0),
        step=5.0,
        help="Upper frequency bound for analysis"
    )

    st.session_state.plot_settings['p_bandwidth'] = st.number_input(
        "Peak Bandwidth (Hz)",
        min_value=0.5,
        max_value=10.0,
        value=safe_get_plot_setting('p_bandwidth', 2.0),
        step=0.5,
        help="Bandwidth for peak frequency detection"
    )

    # Add kmax parameter (from original implementation)
    # Calculate sampling frequency for parameter limits
    sampling_freq = 1.0 / st.session_state.dt if hasattr(st.session_state, 'dt') and st.session_state.dt > 0 else 250.0
    st.session_state.plot_settings['kmax'] = st.number_input(
        "Kmax Parameter (Hz)",
        min_value=0.0,
        max_value=sampling_freq,
        value=safe_get_plot_setting('kmax', 120.0),
        step=1.0,
        help="Maximum frequency parameter for spectral analysis"
    )

with col3:
    st.markdown("#### Advanced Parameters")
    st.session_state.plot_settings['roll_percent'] = st.number_input(
        "Rolloff Percentage",
        min_value=0.1,
        max_value=1.0,
        value=safe_get_plot_setting('roll_percent', 0.80),
        step=0.05,
        help="Spectral rolloff percentage threshold"
    )

    # Add use_band_limited parameter (from original implementation)
    st.session_state.plot_settings['use_band_limited'] = st.checkbox(
        "Use Band-Limited Descriptors",
        value=safe_get_plot_setting('use_band_limited', False),
        help="Use band-limited analysis within b1-b2 frequency range"
    )

    # WOSS-specific parameters (always show, not conditional)
    st.session_state.plot_settings['epsilon'] = st.number_input(
        "WOSS Epsilon",
        min_value=1e-6,
        max_value=1e-2,
        value=safe_get_plot_setting('epsilon', 1e-4),
        format="%.2e",
        help="Small value to prevent division by zero in WOSS calculation"
    )

    # Add fdom_exponent parameter (from original implementation)
    st.session_state.plot_settings['fdom_exponent'] = st.number_input(
        "Frequency Domain Exponent",
        min_value=1.0,
        max_value=5.0,
        value=safe_get_plot_setting('fdom_exponent', 2.0),
        step=0.1,
        help="Exponent for frequency domain calculations"
    )

st.markdown("---")

# --- Display Parameters ---
st.markdown("### 🎨 Display Parameters")

col1, col2 = st.columns(2)

with col1:
    st.markdown("#### Frequency Display Limits")
    
    # Calculate Nyquist frequency
    nyquist_freq = 0.5 / st.session_state.dt if st.session_state.get('dt', 0) > 0 else 125.0
    
    st.session_state.plot_settings['freq_min'] = st.number_input(
        "Frequency Min (Hz)",
        min_value=0.0,
        max_value=nyquist_freq,
        value=safe_get_plot_setting('freq_min', 0.0),
        step=1.0,
        help="Minimum frequency for spectrogram display"
    )

    st.session_state.plot_settings['freq_max'] = st.number_input(
        "Frequency Max (Hz)",
        min_value=safe_get_plot_setting('freq_min', 0.0),
        max_value=nyquist_freq,
        value=safe_get_plot_setting('freq_max', min(125.0, nyquist_freq)),
        step=1.0,
        help="Maximum frequency for spectrogram display"
    )

with col2:
    st.markdown("#### Time Display Limits")
    
    st.session_state.plot_settings['time_min'] = st.number_input(
        "Time Min (s)",
        min_value=0.0,
        max_value=10.0,
        value=safe_get_plot_setting('time_min', 0.0),
        step=0.1,
        help="Minimum time for display"
    )

    st.session_state.plot_settings['time_max'] = st.number_input(
        "Time Max (s)",
        min_value=safe_get_plot_setting('time_min', 0.0),
        max_value=20.0,
        value=safe_get_plot_setting('time_max', 4.0),
        step=0.1,
        help="Maximum time for display"
    )

# Colormap selection
st.markdown("#### Colormap Settings")
available_colormaps = ['viridis', 'plasma', 'inferno', 'magma', 'rainbow', 'RdBu', 'seismic', 'coolwarm', 'gray']

current_colormap = safe_get_plot_setting('section_colormap', 'gray')
default_index = available_colormaps.index(current_colormap) if current_colormap in available_colormaps else available_colormaps.index('gray')

st.session_state.plot_settings['section_colormap'] = st.selectbox(
    "Seismic Section Colormap",
    available_colormaps,
    index=default_index,
    help="Colormap for seismic section background"
)

st.markdown("---")

# --- Statistics and Validation ---
st.markdown("### 📈 Statistics & Validation")

col1, col2 = st.columns(2)

with col1:
    # Statistics calculation parameters
    st.session_state.plot_settings['sample_percent'] = st.slider(
        "Sample Percentage for Statistics (%)",
        min_value=1.0,
        max_value=20.0,
        value=safe_get_plot_setting('sample_percent', 5.0),
        step=1.0,
        help="Percentage of traces to sample for statistics calculation"
    )

    st.session_state.plot_settings['max_traces_for_stats'] = st.number_input(
        "Max Traces for Statistics",
        min_value=100,
        max_value=5000,
        value=safe_get_plot_setting('max_traces_for_stats', 1000),
        step=100,
        help="Maximum number of traces for statistics calculation"
    )

with col2:
    # Parameter validation and GPU memory check
    if st.button("🔍 Validate Configuration", type="secondary"):
        validation_errors = []
        
        # Check frequency parameters
        if safe_get_plot_setting('freq_min', 0) >= safe_get_plot_setting('freq_max', 125):
            validation_errors.append("Frequency min must be less than frequency max")

        # Check time parameters
        if safe_get_plot_setting('time_min', 0) >= safe_get_plot_setting('time_max', 4):
            validation_errors.append("Time min must be less than time max")

        # Check spectral parameters
        if safe_get_plot_setting('b1', 5) >= safe_get_plot_setting('b2', 40):
            validation_errors.append("Low frequency must be less than high frequency")

        # Check kmax parameter
        if safe_get_plot_setting('kmax', 120) <= 0:
            validation_errors.append("Kmax parameter must be greater than 0")

        # Check shape parameter
        if safe_get_plot_setting('shape', 0.35) < 0:
            validation_errors.append("Shape parameter must be non-negative")
        
        if validation_errors:
            for error in validation_errors:
                st.error(f"❌ {error}")
        else:
            st.success("✅ Configuration validated successfully!")
            
            # Show GPU memory estimation
            if gpu_status['available'] and selected_outputs:
                est_memory = len(selected_outputs) * st.session_state.get('trace_count', 1000) * 4 / (1024**3)
                st.info(f"📊 Estimated GPU memory usage: {est_memory:.2f} GB")

# Configuration complete button
st.markdown("---")
if st.button("✅ Configuration Complete", type="primary", use_container_width=True):
    if selected_outputs:
        st.session_state.configuration_complete = True
        st.success("🎉 Display configuration completed successfully!")

        # Navigation buttons after successful configuration
        st.markdown("### 🧭 Navigation")
        col1, col2, col3 = st.columns([1, 1, 1])

        with col1:
            if st.button("🏠 Back to Dashboard", use_container_width=True, help="Return to main dashboard"):
                st.switch_page("app.py")

        with col2:
            if st.button("📁 Step 1: Load Data", use_container_width=True, help="Go back to data loading"):
                st.switch_page("pages/1_load_data.py")

        with col3:
            if st.button("🎯 Step 3: Select Area", use_container_width=True, type="primary", help="Proceed to area selection"):
                st.switch_page("pages/3_select_area.py")

        # Workflow progress indicator
        st.markdown("### 📊 Workflow Progress")
        progress_col1, progress_col2 = st.columns([3, 1])

        with progress_col1:
            # Calculate progress
            steps_completed = 2  # Steps 1 and 2 are complete
            if st.session_state.get('area_selected'):
                steps_completed += 1
            if st.session_state.get('analysis_complete'):
                steps_completed += 1
            if st.session_state.get('export_complete'):
                steps_completed += 1

            progress_percentage = (steps_completed / 5) * 100
            st.progress(progress_percentage / 100)

        with progress_col2:
            st.metric("Completed", f"{steps_completed}/5 steps")

        # Step status indicators
        step_status_col1, step_status_col2, step_status_col3 = st.columns(3)

        with step_status_col1:
            st.success("✅ Step 1: Data Loaded")

        with step_status_col2:
            st.success("✅ Step 2: Display Configured")

        with step_status_col3:
            if st.session_state.get('area_selected'):
                st.success("✅ Step 3: Area Selected")
            else:
                st.info("⏳ Step 3: Select Area")

    else:
        st.error("❌ Please select at least one output before proceeding")

# Help section
with st.expander("ℹ️ Configuration Help"):
    st.markdown("""
    ### GPU Processing Tips:
    - **Conservative Memory**: Stable processing, slower performance
    - **Balanced Memory**: Optimal balance of speed and stability  
    - **Aggressive Memory**: Maximum speed, may cause memory issues
    
    ### Parameter Guidelines:
    - **Window Length**: 20-50ms typical for seismic analysis
    - **Frequency Range**: Adjust based on your data's frequency content
    - **Shape Parameter**: Controls spectral window shape (0.1-1.0 typical)
    - **Kmax Parameter**: Maximum frequency for analysis (usually < Nyquist)
    - **Band-Limited**: Enable to restrict analysis to b1-b2 frequency range
    - **Output Selection**: More outputs = higher memory usage
    
    ### Performance Optimization:
    - GPU processing is 10-50x faster than CPU
    - Batch size automatically optimized based on GPU memory
    - Real-time memory monitoring prevents crashes
    """)
