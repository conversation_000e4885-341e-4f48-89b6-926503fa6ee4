# WOSS Seismic Analysis Tool - GPU-Optimized Requirements
# Core dependencies for the modular, GPU-accelerated WOSS application

# Streamlit framework
streamlit>=1.28.0

# Scientific computing
numpy>=1.21.0
scipy>=1.7.0
pandas>=1.3.0

# Seismic data processing
segyio>=1.9.0

# Visualization
plotly>=5.0.0
matplotlib>=3.5.0

# Progress bars and utilities
tqdm>=4.62.0

# System monitoring
psutil>=5.8.0

# Optional GPU acceleration (install separately if GPU available)
# Option 1: CuPy (preferred for array operations)
# cupy-cuda11x>=11.0.0  # For CUDA 11.x
# cupy-cuda12x>=12.0.0  # For CUDA 12.x

# Option 2: PyTorch (alternative GPU backend)
# torch>=1.12.0  # CPU version
# torch>=1.12.0+cu117  # CUDA 11.7 version (install from pytorch.org)
# torchvision>=0.13.0  # Optional, for additional GPU utilities

# Development and testing (optional)
pytest>=6.0.0
pytest-cov>=3.0.0

# File handling
openpyxl>=3.0.0  # For Excel file support
xlsxwriter>=3.0.0  # For Excel export

# Additional utilities
pathlib2>=2.3.0  # For Python < 3.4 compatibility (if needed)
