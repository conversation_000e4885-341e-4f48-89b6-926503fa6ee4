# pages/4_analyze_data.py
import streamlit as st
import numpy as np
import logging
from tqdm import tqdm
import time
from utils.processing_gpu import (
    process_inline_analysis_gpu, 
    process_crossline_analysis_gpu,
    process_aoi_analysis_gpu,
    process_polyline_analysis_gpu
)
from utils.gpu_utils import get_optimal_processing_config, clear_gpu_memory
from utils.visualization import create_analysis_plots
from common.session_state import initialize_session_state
from common.constants import ANALYSIS_MODES

st.set_page_config(page_title="Analyze Data", layout="wide")
st.title("Step 4: Analyze Data (GPU-Accelerated)")

# Initialize session state
initialize_session_state()

# Check prerequisites
if not st.session_state.get('header_loader'):
    st.error("❌ No data loaded. Please go to Step 1 to load your SEG-Y file.")
    st.stop()

if not st.session_state.get('area_selected'):
    st.error("❌ No analysis area selected. Please go to Step 3 to select an analysis area.")
    st.stop()

if not st.session_state.get('plot_settings'):
    st.error("❌ No configuration set. Please go to Step 2 to configure display settings.")
    st.stop()

# Display GPU status
st.markdown("### 🚀 Processing Status")
if st.session_state.get('gpu_available'):
    col1, col2, col3 = st.columns(3)
    with col1:
        st.success("✅ GPU Acceleration Ready")
    with col2:
        st.info(f"Backend: {st.session_state.get('gpu_backend', 'Unknown')}")
    with col3:
        if st.session_state.get('gpu_device_info'):
            st.info(f"Device: {st.session_state.gpu_device_info}")
else:
    st.warning("⚠️ Using CPU processing")

st.markdown("---")

# Get analysis parameters
header_loader = st.session_state.header_loader
segy_path = st.session_state.segy_file_path
dt = st.session_state.dt
plot_settings = st.session_state.plot_settings
analysis_mode = st.session_state.get('analysis_mode', 'Unknown')

# Display analysis summary
st.markdown("### 📊 Analysis Summary")
col1, col2 = st.columns(2)

with col1:
    st.write(f"**Analysis Mode:** {analysis_mode}")
    st.write(f"**SEG-Y File:** {st.session_state.get('segy_filename', 'Unknown')}")
    st.write(f"**Sampling Interval:** {dt:.2f} ms")

with col2:
    # Estimate processing configuration
    if "Single inline" in analysis_mode and st.session_state.get('selected_inline'):
        inline_mask = header_loader.inlines == st.session_state.selected_inline
        trace_count = np.sum(inline_mask)
        st.write(f"**Selected Inline:** {st.session_state.selected_inline}")
        st.write(f"**Traces to Process:** {trace_count}")
    elif "Single crossline" in analysis_mode and st.session_state.get('selected_crossline'):
        crossline_mask = header_loader.crosslines == st.session_state.selected_crossline
        trace_count = np.sum(crossline_mask)
        st.write(f"**Selected Crossline:** {st.session_state.selected_crossline}")
        st.write(f"**Traces to Process:** {trace_count}")
    elif "AOI" in analysis_mode and st.session_state.get('aoi_bounds'):
        bounds = st.session_state.aoi_bounds
        inline_mask = (header_loader.inlines >= bounds['inline_min']) & (header_loader.inlines <= bounds['inline_max'])
        crossline_mask = (header_loader.crosslines >= bounds['crossline_min']) & (header_loader.crosslines <= bounds['crossline_max'])
        aoi_mask = inline_mask & crossline_mask
        trace_count = np.sum(aoi_mask)
        st.write(f"**AOI Bounds:** IL {bounds['inline_min']}-{bounds['inline_max']}, XL {bounds['crossline_min']}-{bounds['crossline_max']}")
        st.write(f"**Traces to Process:** {trace_count}")
    elif "Polyline" in analysis_mode and st.session_state.get('polyline_indices'):
        trace_count = len(st.session_state.polyline_indices)
        st.write(f"**Polyline Traces:** {trace_count}")
    else:
        trace_count = 0
        st.write("**Traces to Process:** Unknown")

# Get processing configuration
if trace_count > 0:
    config = get_optimal_processing_config(analysis_mode, trace_count)
    
    # Display processing configuration
    st.markdown("### ⚙️ Processing Configuration")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Backend", config['backend'])
    with col2:
        st.metric("Batch Size", config['batch_size'])
    with col3:
        st.metric("Est. Batches", config['estimated_batches'])
    with col4:
        est_time = config['estimated_batches'] * 2  # Rough estimate: 2 seconds per batch
        st.metric("Est. Time", f"{est_time}s")

st.markdown("---")

# Analysis execution
def execute_analysis():
    """Execute the GPU-accelerated analysis based on selected mode."""
    try:
        st.markdown("### 🚀 Analysis Execution")
        
        # Create progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        start_time = time.time()
        
        # Execute based on analysis mode
        if "Single inline" in analysis_mode:
            status_text.text("Starting GPU-accelerated inline analysis...")
            results = process_inline_analysis_gpu(
                segy_path, st.session_state.selected_inline, 
                header_loader, dt, plot_settings
            )
            
        elif "Single crossline" in analysis_mode:
            status_text.text("Starting GPU-accelerated crossline analysis...")
            results = process_crossline_analysis_gpu(
                segy_path, st.session_state.selected_crossline,
                header_loader, dt, plot_settings
            )
            
        elif "AOI" in analysis_mode:
            status_text.text("Starting GPU-accelerated AOI analysis...")
            bounds = st.session_state.aoi_bounds
            inline_range = [bounds['inline_min'], bounds['inline_max']]
            crossline_range = [bounds['crossline_min'], bounds['crossline_max']]
            results = process_aoi_analysis_gpu(
                segy_path, inline_range, crossline_range,
                header_loader, dt, plot_settings
            )
            
        elif "Polyline" in analysis_mode:
            status_text.text("Starting GPU-accelerated polyline analysis...")
            results = process_polyline_analysis_gpu(
                segy_path, st.session_state.polyline_indices,
                header_loader, dt, plot_settings
            )
        else:
            st.error(f"Analysis mode '{analysis_mode}' not implemented yet.")
            return None
        
        progress_bar.progress(1.0)
        end_time = time.time()
        processing_time = end_time - start_time
        
        status_text.success(f"✅ Analysis completed in {processing_time:.1f} seconds using {results['processing_config']['backend']} backend")
        
        # Store results in session state
        st.session_state.analysis_results = results
        st.session_state.analysis_complete = True
        st.session_state.processing_time = processing_time
        
        # Clear GPU memory
        clear_gpu_memory()
        
        return results
        
    except Exception as e:
        st.error(f"❌ Analysis failed: {str(e)}")
        logging.error(f"Analysis execution failed: {e}")
        clear_gpu_memory()
        return None

# Analysis execution button
if not st.session_state.get('analysis_complete'):
    if st.button("🚀 Start GPU-Accelerated Analysis", type="primary", use_container_width=True):
        with st.spinner("Executing GPU-accelerated analysis..."):
            results = execute_analysis()
            if results:
                st.rerun()

# Display results if analysis is complete
if st.session_state.get('analysis_complete') and st.session_state.get('analysis_results'):
    st.markdown("---")
    st.markdown("### 📈 Analysis Results")
    
    results = st.session_state.analysis_results
    processing_time = st.session_state.get('processing_time', 0)
    
    # Results summary
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Processing Time", f"{processing_time:.1f}s")
    with col2:
        st.metric("Backend Used", results['processing_config']['backend'])
    with col3:
        st.metric("Traces Processed", len(results['trace_data']))
    with col4:
        st.metric("Descriptors Calculated", len(results['descriptors']))
    
    # Results visualization
    if st.checkbox("📊 Show Results Visualization", value=True):
        try:
            # Create visualization plots
            fig = create_analysis_plots(results, analysis_mode, plot_settings)
            if fig:
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("Visualization not available for this analysis mode yet.")
        except Exception as e:
            st.warning(f"Could not create visualization: {str(e)}")
    
    # Export readiness
    st.success("✅ Analysis complete! You can now proceed to Step 5 to export results.")
    
    # Navigation
    if st.button("📤 Go to Export Results", type="secondary", use_container_width=True):
        st.switch_page("pages/5_export_results.py")

# Reset analysis button
if st.session_state.get('analysis_complete'):
    st.markdown("---")
    if st.button("🔄 Reset Analysis", help="Clear current results and start over"):
        # Clear analysis results
        for key in ['analysis_results', 'analysis_complete', 'processing_time']:
            if key in st.session_state:
                del st.session_state[key]
        clear_gpu_memory()
        st.rerun()
