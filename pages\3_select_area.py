# pages/3_select_area.py
import streamlit as st
import numpy as np
import pandas as pd
from common.constants import ANALYSIS_MODES
from common.session_state import initialize_session_state
from utils.gpu_utils import get_optimal_processing_config
from utils.general_utils import find_traces_near_polyline, parse_polyline_string
import logging

st.set_page_config(page_title="Select Analysis Area", layout="wide")
st.title("Step 3: Select Analysis Area (GPU-Optimized)")

# Initialize session state
initialize_session_state()

# Check for loaded data
if not st.session_state.get('header_loader'):
    st.error("❌ No data loaded. Please go to Step 1 to load your SEG-Y file.")
    st.stop()

# Display GPU status
if st.session_state.get('gpu_available'):
    st.success("🚀 GPU acceleration enabled for analysis")
else:
    st.warning("💻 Using CPU processing (GPU not available)")

header_loader = st.session_state.header_loader

# Sidebar - Analysis mode selection
st.sidebar.header("🎯 Analysis Mode Selection")
selected_mode = st.sidebar.selectbox(
    "Select analysis mode:",
    ANALYSIS_MODES,
    key="analysis_mode_selector"
)

# Store selected mode
st.session_state.selected_analysis_mode = selected_mode

# Get estimated processing configuration
if selected_mode:
    # Estimate number of traces for this mode
    if "Single inline" in selected_mode:
        unique_inlines = np.unique(header_loader.inlines)
        est_traces = len(header_loader.inlines) // len(unique_inlines)
    elif "Single crossline" in selected_mode:
        unique_crosslines = np.unique(header_loader.crosslines)
        est_traces = len(header_loader.crosslines) // len(unique_crosslines)
    else:
        est_traces = len(header_loader.unique_indices)
    
    config = get_optimal_processing_config(selected_mode, est_traces)
    
    # Display processing info
    with st.sidebar.expander("🔧 Processing Configuration"):
        st.write(f"**Backend:** {config['backend']}")
        st.write(f"**Optimal batch size:** {config['batch_size']}")
        st.write(f"**Estimated batches:** {config['estimated_batches']}")

# Main content - Mode-specific UI
st.markdown("---")

if "Single inline" in selected_mode:
    render_inline_selection()
elif "Single crossline" in selected_mode:
    render_crossline_selection()  
elif "AOI" in selected_mode:
    render_aoi_selection()
elif "Polyline" in selected_mode:
    render_polyline_selection()
elif "well markers" in selected_mode:
    render_well_marker_selection()

def render_inline_selection():
    """Render single inline selection interface."""
    st.subheader("🔗 Single Inline Analysis (GPU-Optimized)")
    
    # Get available inlines
    unique_inlines = np.unique(header_loader.inlines)
    inline_range = f"{unique_inlines.min()} - {unique_inlines.max()}"
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.info(f"📊 Available inlines: {inline_range} ({len(unique_inlines)} unique inlines)")
        
        selected_inline = st.selectbox(
            "Select inline number:",
            unique_inlines,
            help="Choose an inline for GPU-accelerated analysis of all crosslines"
        )
        
        # Show trace count for this inline
        inline_mask = header_loader.inlines == selected_inline
        trace_count = np.sum(inline_mask)
        crosslines = header_loader.crosslines[inline_mask]
        crossline_range = f"{crosslines.min()} - {crosslines.max()}"
        
        st.write(f"**Selected inline {selected_inline}:**")
        st.write(f"• {trace_count} traces")
        st.write(f"• Crosslines: {crossline_range}")
        
    with col2:
        # Show GPU optimization info
        config = get_optimal_processing_config(selected_mode, trace_count)
        st.markdown("### 🚀 GPU Optimization")
        st.metric("Processing Backend", config['backend'])
        st.metric("Batch Size", config['batch_size'])
        st.metric("Est. Processing Time", f"{config['estimated_batches']} batches")
    
    # Process button
    if st.button("🚀 Start GPU-Accelerated Analysis", type="primary", use_container_width=True):
        st.session_state.selected_inline = selected_inline
        st.session_state.area_selected = True
        st.session_state.analysis_mode = selected_mode
        st.success(f"✅ Inline {selected_inline} selected for GPU processing!")

        # Navigation buttons after successful area selection
        st.markdown("### 🧭 Navigation")
        nav_col1, nav_col2, nav_col3 = st.columns([1, 1, 1])

        with nav_col1:
            if st.button("🏠 Back to Dashboard", use_container_width=True, help="Return to main dashboard", key="nav_dashboard_inline"):
                st.switch_page("app.py")

        with nav_col2:
            if st.button("⚙️ Step 2: Configure Display", use_container_width=True, help="Go back to display configuration", key="nav_step2_inline"):
                st.switch_page("pages/2_configure_display.py")

        with nav_col3:
            if st.button("🚀 Step 4: Analyze Data", use_container_width=True, type="primary", help="Proceed to data analysis", key="nav_step4_inline"):
                st.switch_page("pages/4_analyze_data.py")

def render_crossline_selection():
    """Render single crossline selection interface."""
    st.subheader("📏 Single Crossline Analysis (GPU-Optimized)")
    
    unique_crosslines = np.unique(header_loader.crosslines)
    crossline_range = f"{unique_crosslines.min()} - {unique_crosslines.max()}"
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.info(f"📊 Available crosslines: {crossline_range} ({len(unique_crosslines)} unique crosslines)")
        
        selected_crossline = st.selectbox(
            "Select crossline number:",
            unique_crosslines,
            help="Choose a crossline for GPU-accelerated analysis of all inlines"
        )
        
        # Show trace count for this crossline
        crossline_mask = header_loader.crosslines == selected_crossline
        trace_count = np.sum(crossline_mask)
        inlines = header_loader.inlines[crossline_mask]
        inline_range = f"{inlines.min()} - {inlines.max()}"
        
        st.write(f"**Selected crossline {selected_crossline}:**")
        st.write(f"• {trace_count} traces")
        st.write(f"• Inlines: {inline_range}")
        
    with col2:
        # Show GPU optimization info
        config = get_optimal_processing_config(selected_mode, trace_count)
        st.markdown("### 🚀 GPU Optimization")
        st.metric("Processing Backend", config['backend'])
        st.metric("Batch Size", config['batch_size'])
        st.metric("Est. Processing Time", f"{config['estimated_batches']} batches")
    
    if st.button("🚀 Start GPU-Accelerated Analysis", type="primary", use_container_width=True):
        st.session_state.selected_crossline = selected_crossline
        st.session_state.area_selected = True
        st.session_state.analysis_mode = selected_mode
        st.success(f"✅ Crossline {selected_crossline} selected for GPU processing!")

        # Navigation buttons after successful area selection
        st.markdown("### 🧭 Navigation")
        nav_col1, nav_col2, nav_col3 = st.columns([1, 1, 1])

        with nav_col1:
            if st.button("🏠 Back to Dashboard", use_container_width=True, help="Return to main dashboard", key="nav_dashboard_crossline"):
                st.switch_page("app.py")

        with nav_col2:
            if st.button("⚙️ Step 2: Configure Display", use_container_width=True, help="Go back to display configuration", key="nav_step2_crossline"):
                st.switch_page("pages/2_configure_display.py")

        with nav_col3:
            if st.button("🚀 Step 4: Analyze Data", use_container_width=True, type="primary", help="Proceed to data analysis", key="nav_step4_crossline"):
                st.switch_page("pages/4_analyze_data.py")

def render_aoi_selection():
    """Render Area of Interest (AOI) selection interface."""
    st.subheader("🎯 Area of Interest (AOI) Analysis (GPU-Optimized)")
    
    # Get data ranges
    inline_min, inline_max = int(header_loader.inlines.min()), int(header_loader.inlines.max())
    crossline_min, crossline_max = int(header_loader.crosslines.min()), int(header_loader.crosslines.max())
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.info(f"📊 Survey extent: IL {inline_min}-{inline_max}, XL {crossline_min}-{crossline_max}")
        
        # AOI selection
        st.markdown("#### Define AOI Bounds:")
        
        col_il1, col_il2 = st.columns(2)
        with col_il1:
            aoi_inline_min = st.number_input("Inline Min", value=inline_min, min_value=inline_min, max_value=inline_max)
        with col_il2:
            aoi_inline_max = st.number_input("Inline Max", value=inline_max, min_value=inline_min, max_value=inline_max)
            
        col_xl1, col_xl2 = st.columns(2)
        with col_xl1:
            aoi_crossline_min = st.number_input("Crossline Min", value=crossline_min, min_value=crossline_min, max_value=crossline_max)
        with col_xl2:
            aoi_crossline_max = st.number_input("Crossline Max", value=crossline_max, min_value=crossline_min, max_value=crossline_max)
        
        # Calculate AOI stats
        inline_mask = (header_loader.inlines >= aoi_inline_min) & (header_loader.inlines <= aoi_inline_max)
        crossline_mask = (header_loader.crosslines >= aoi_crossline_min) & (header_loader.crosslines <= aoi_crossline_max)
        aoi_mask = inline_mask & crossline_mask
        aoi_trace_count = np.sum(aoi_mask)
        
        st.write(f"**AOI Coverage:**")
        st.write(f"• IL {aoi_inline_min}-{aoi_inline_max}, XL {aoi_crossline_min}-{aoi_crossline_max}")
        st.write(f"• {aoi_trace_count} traces selected")
        
    with col2:
        # Show GPU optimization info for AOI
        config = get_optimal_processing_config(selected_mode, aoi_trace_count)
        st.markdown("### 🚀 GPU Optimization")
        st.metric("Processing Backend", config['backend'])
        st.metric("Batch Size", config['batch_size'])
        st.metric("Est. Processing Time", f"{config['estimated_batches']} batches")
        
        # Memory estimation
        if aoi_trace_count > 0:
            est_memory_gb = (aoi_trace_count * 1000 * 4) / (1024**3)  # Rough estimate
            st.metric("Est. Memory Usage", f"{est_memory_gb:.1f} GB")
    
    if aoi_trace_count > 0:
        if st.button("🚀 Start GPU-Accelerated AOI Analysis", type="primary", use_container_width=True):
            st.session_state.aoi_bounds = {
                'inline_min': aoi_inline_min, 'inline_max': aoi_inline_max,
                'crossline_min': aoi_crossline_min, 'crossline_max': aoi_crossline_max
            }
            st.session_state.area_selected = True
            st.session_state.analysis_mode = selected_mode
            st.success(f"✅ AOI selected: {aoi_trace_count} traces for GPU processing!")

            # Navigation buttons after successful area selection
            st.markdown("### 🧭 Navigation")
            nav_col1, nav_col2, nav_col3 = st.columns([1, 1, 1])

            with nav_col1:
                if st.button("🏠 Back to Dashboard", use_container_width=True, help="Return to main dashboard", key="nav_dashboard_aoi"):
                    st.switch_page("app.py")

            with nav_col2:
                if st.button("⚙️ Step 2: Configure Display", use_container_width=True, help="Go back to display configuration", key="nav_step2_aoi"):
                    st.switch_page("pages/2_configure_display.py")

            with nav_col3:
                if st.button("🚀 Step 4: Analyze Data", use_container_width=True, type="primary", help="Proceed to data analysis", key="nav_step4_aoi"):
                    st.switch_page("pages/4_analyze_data.py")
    else:
        st.warning("⚠️ No traces found in the selected AOI bounds")

def render_polyline_selection():
    """Render polyline analysis selection interface."""
    st.subheader("📍 Polyline Analysis (GPU-Optimized)")

    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown("#### Define Polyline Path:")

        # Polyline input methods
        input_method = st.radio(
            "Input method:",
            ["Manual coordinates", "Upload CSV file"],
            help="Choose how to specify the polyline path"
        )

        if input_method == "Manual coordinates":
            polyline_text = st.text_area(
                "Enter polyline coordinates (X,Y pairs):",
                placeholder="x1,y1\nx2,y2\nx3,y3\n...",
                help="Enter X,Y coordinate pairs, one per line"
            )

            tolerance = st.number_input(
                "Tolerance (distance from polyline):",
                value=100.0, min_value=1.0, max_value=1000.0,
                help="Maximum distance from polyline to include traces"
            )

            if polyline_text:
                try:
                    polyline_points = parse_polyline_string(polyline_text)
                    polyline_indices = find_traces_near_polyline(header_loader, polyline_points, tolerance)

                    st.write(f"**Polyline Analysis:**")
                    st.write(f"• {len(polyline_points)} polyline vertices")
                    st.write(f"• {len(polyline_indices)} traces within {tolerance}m")

                    if len(polyline_indices) > 0:
                        st.session_state.polyline_indices = polyline_indices

                except Exception as e:
                    st.error(f"Error parsing polyline: {e}")
                    polyline_indices = []
            else:
                polyline_indices = []

        else:  # CSV upload
            uploaded_file = st.file_uploader(
                "Upload polyline CSV file",
                type=['csv'],
                help="CSV file with X,Y columns"
            )

            if uploaded_file:
                try:
                    df = pd.read_csv(uploaded_file)
                    if 'X' in df.columns and 'Y' in df.columns:
                        polyline_points = list(zip(df['X'], df['Y']))

                        tolerance = st.number_input(
                            "Tolerance (distance from polyline):",
                            value=100.0, min_value=1.0, max_value=1000.0
                        )

                        polyline_indices = find_traces_near_polyline(header_loader, polyline_points, tolerance)

                        st.write(f"**Polyline Analysis:**")
                        st.write(f"• {len(polyline_points)} polyline vertices")
                        st.write(f"• {len(polyline_indices)} traces within {tolerance}m")

                        if len(polyline_indices) > 0:
                            st.session_state.polyline_indices = polyline_indices
                    else:
                        st.error("CSV file must contain 'X' and 'Y' columns")
                        polyline_indices = []
                except Exception as e:
                    st.error(f"Error reading CSV file: {e}")
                    polyline_indices = []
            else:
                polyline_indices = []

    with col2:
        # Show GPU optimization info for polyline
        if 'polyline_indices' in st.session_state:
            trace_count = len(st.session_state.polyline_indices)
            config = get_optimal_processing_config(selected_mode, trace_count)

            st.markdown("### 🚀 GPU Optimization")
            st.metric("Processing Backend", config['backend'])
            st.metric("Batch Size", config['batch_size'])
            st.metric("Est. Processing Time", f"{config['estimated_batches']} batches")

    if st.session_state.get('polyline_indices') and len(st.session_state.polyline_indices) > 0:
        if st.button("🚀 Start GPU-Accelerated Polyline Analysis", type="primary", use_container_width=True):
            st.session_state.area_selected = True
            st.session_state.analysis_mode = selected_mode
            st.success(f"✅ Polyline selected: {len(st.session_state.polyline_indices)} traces for GPU processing!")

            # Navigation buttons after successful area selection
            st.markdown("### 🧭 Navigation")
            nav_col1, nav_col2, nav_col3 = st.columns([1, 1, 1])

            with nav_col1:
                if st.button("🏠 Back to Dashboard", use_container_width=True, help="Return to main dashboard", key="nav_dashboard_polyline"):
                    st.switch_page("app.py")

            with nav_col2:
                if st.button("⚙️ Step 2: Configure Display", use_container_width=True, help="Go back to display configuration", key="nav_step2_polyline"):
                    st.switch_page("pages/2_configure_display.py")

            with nav_col3:
                if st.button("🚀 Step 4: Analyze Data", use_container_width=True, type="primary", help="Proceed to data analysis", key="nav_step4_polyline"):
                    st.switch_page("pages/4_analyze_data.py")

def render_well_marker_selection():
    """Render well marker selection interface."""
    st.subheader("🎯 Well Marker Analysis (GPU-Optimized)")

    if not st.session_state.get('well_data'):
        st.warning("⚠️ No well data loaded. Please load well data in Step 1.")
        return

    well_data = st.session_state.well_data

    col1, col2 = st.columns([2, 1])

    with col1:
        st.info(f"📊 Available wells: {len(well_data)} wells loaded")

        # Well marker selection
        available_markers = well_data.columns.tolist()
        selected_markers = st.multiselect(
            "Select markers for analysis:",
            available_markers,
            help="Choose which markers to analyze with GPU acceleration"
        )

        if selected_markers:
            # Count traces for selected markers
            valid_wells = well_data.dropna(subset=selected_markers)
            trace_count = len(valid_wells) * len(selected_markers)

            st.write(f"**Analysis Summary:**")
            st.write(f"• {len(selected_markers)} markers selected")
            st.write(f"• {len(valid_wells)} valid wells")
            st.write(f"• {trace_count} total analyses")

    with col2:
        if selected_markers:
            config = get_optimal_processing_config(selected_mode, trace_count)

            st.markdown("### 🚀 GPU Optimization")
            st.metric("Processing Backend", config['backend'])
            st.metric("Batch Size", config['batch_size'])
            st.metric("Est. Processing Time", f"{config['estimated_batches']} batches")

    if selected_markers:
        if st.button("🚀 Start GPU-Accelerated Well Analysis", type="primary", use_container_width=True):
            st.session_state.selected_markers = selected_markers
            st.session_state.area_selected = True
            st.session_state.analysis_mode = selected_mode
            st.success(f"✅ {len(selected_markers)} markers selected for GPU processing!")

            # Navigation buttons after successful area selection
            st.markdown("### 🧭 Navigation")
            nav_col1, nav_col2, nav_col3 = st.columns([1, 1, 1])

            with nav_col1:
                if st.button("🏠 Back to Dashboard", use_container_width=True, help="Return to main dashboard", key="nav_dashboard_well"):
                    st.switch_page("app.py")

            with nav_col2:
                if st.button("⚙️ Step 2: Configure Display", use_container_width=True, help="Go back to display configuration", key="nav_step2_well"):
                    st.switch_page("pages/2_configure_display.py")

            with nav_col3:
                if st.button("🚀 Step 4: Analyze Data", use_container_width=True, type="primary", help="Proceed to data analysis", key="nav_step4_well"):
                    st.switch_page("pages/4_analyze_data.py")
