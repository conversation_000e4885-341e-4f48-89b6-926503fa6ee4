"""
CPU Implementation of Spectral Descriptor Functions

This module provides CPU-based implementations of the spectral descriptor functions
that are used as fallbacks when GPU implementations are not available.

These functions provide the same interface as their GPU counterparts but use
NumPy and SciPy for computation instead of CuPy.
"""

import numpy as np
import scipy.fft as fft
from scipy.signal import hilbert
import logging


def dlogst_spec_descriptor_cpu(trace, dt, fmax=None, **kwargs):
    """
    CPU implementation of spectral descriptor calculation for single trace.
    
    Parameters:
    -----------
    trace : array-like
        Input seismic trace data
    dt : float
        Time sampling interval
    fmax : float, optional
        Maximum frequency for analysis
    **kwargs : dict
        Additional parameters for compatibility
    
    Returns:
    --------
    tuple
        (frequencies, spectral_descriptor) where:
        - frequencies: frequency array
        - spectral_descriptor: computed spectral descriptor values
    """
    try:
        # Convert to numpy array if needed
        trace = np.asarray(trace, dtype=np.float64)
        
        # Remove mean
        trace = trace - np.mean(trace)
        
        # Apply windowing to reduce spectral leakage
        window = np.hanning(len(trace))
        trace_windowed = trace * window
        
        # Compute FFT
        n_fft = len(trace_windowed)
        fft_trace = fft.fft(trace_windowed)
        
        # Compute frequency array
        frequencies = fft.fftfreq(n_fft, dt)
        
        # Take only positive frequencies
        n_pos = n_fft // 2
        frequencies = frequencies[:n_pos]
        fft_trace = fft_trace[:n_pos]
        
        # Apply frequency limit if specified
        if fmax is not None:
            freq_mask = frequencies <= fmax
            frequencies = frequencies[freq_mask]
            fft_trace = fft_trace[freq_mask]
        
        # Compute magnitude spectrum
        magnitude_spectrum = np.abs(fft_trace)
        
        # Avoid division by zero
        magnitude_spectrum = np.where(magnitude_spectrum < 1e-10, 1e-10, magnitude_spectrum)
        
        # Compute spectral descriptor (log of magnitude)
        spectral_descriptor = np.log10(magnitude_spectrum)
        
        # Normalize
        spectral_descriptor = spectral_descriptor - np.mean(spectral_descriptor)
        
        logging.info(f"CPU spectral descriptor computed for {len(trace)} samples")
        
        return frequencies, spectral_descriptor
        
    except Exception as e:
        logging.error(f"Error in CPU spectral descriptor calculation: {e}")
        raise


def dlogst_spec_descriptor_cpu_2d_chunked(data, dt, fmax=None, shape=None, kmax=None, 
                                         int_val=None, chunk_size=1000, **kwargs):
    """
    CPU implementation of spectral descriptor calculation for 2D data with chunking.
    
    Parameters:
    -----------
    data : array-like
        2D input seismic data (traces x samples)
    dt : float
        Time sampling interval
    fmax : float, optional
        Maximum frequency for analysis
    shape : tuple, optional
        Output shape
    kmax : int, optional
        Maximum wavenumber
    int_val : float, optional
        Interval value
    chunk_size : int, optional
        Number of traces to process at once
    **kwargs : dict
        Additional parameters for compatibility
    
    Returns:
    --------
    tuple
        (frequencies, spectral_descriptors) where:
        - frequencies: frequency array
        - spectral_descriptors: 2D array of spectral descriptor values
    """
    try:
        # Convert to numpy array
        data = np.asarray(data, dtype=np.float64)
        
        if data.ndim == 1:
            # Single trace case
            return dlogst_spec_descriptor_cpu(data, dt, fmax=fmax, **kwargs)
        
        n_traces, n_samples = data.shape
        
        # Initialize output arrays
        frequencies = None
        spectral_descriptors = []
        
        # Process in chunks to manage memory
        for i in range(0, n_traces, chunk_size):
            end_idx = min(i + chunk_size, n_traces)
            chunk_data = data[i:end_idx]
            
            chunk_results = []
            for j, trace in enumerate(chunk_data):
                freq, spec_desc = dlogst_spec_descriptor_cpu(trace, dt, fmax=fmax, **kwargs)
                
                if frequencies is None:
                    frequencies = freq
                    
                chunk_results.append(spec_desc)
            
            spectral_descriptors.extend(chunk_results)
            
            # Log progress
            if i % (chunk_size * 10) == 0:
                logging.info(f"Processed {min(i + chunk_size, n_traces)}/{n_traces} traces")
        
        # Convert to numpy array
        spectral_descriptors = np.array(spectral_descriptors)
        
        logging.info(f"CPU 2D spectral descriptor computed for {n_traces} traces")
        
        return frequencies, spectral_descriptors
        
    except Exception as e:
        logging.error(f"Error in CPU 2D spectral descriptor calculation: {e}")
        raise


def dlogst_spec_descriptor_cpu_2d_chunked_mag(data, dt, fmax=None, shape=None, kmax=None, 
                                             int_val=None, chunk_size=1000, **kwargs):
    """
    CPU implementation of spectral descriptor calculation with magnitude processing.
    
    This function computes spectral descriptors and includes additional magnitude-based
    processing for enhanced analysis.
    
    Parameters:
    -----------
    data : array-like
        2D input seismic data (traces x samples)
    dt : float
        Time sampling interval
    fmax : float, optional
        Maximum frequency for analysis
    shape : tuple, optional
        Output shape
    kmax : int, optional
        Maximum wavenumber
    int_val : float, optional
        Interval value
    chunk_size : int, optional
        Number of traces to process at once
    **kwargs : dict
        Additional parameters for compatibility
    
    Returns:
    --------
    tuple
        (frequencies, spectral_descriptors, magnitude_data) where:
        - frequencies: frequency array
        - spectral_descriptors: 2D array of spectral descriptor values
        - magnitude_data: magnitude spectrum data
    """
    try:
        # Get basic spectral descriptors
        frequencies, spectral_descriptors = dlogst_spec_descriptor_cpu_2d_chunked(
            data, dt, fmax=fmax, shape=shape, kmax=kmax, int_val=int_val, 
            chunk_size=chunk_size, **kwargs
        )
        
        # Convert to numpy array
        data = np.asarray(data, dtype=np.float64)
        
        if data.ndim == 1:
            data = data.reshape(1, -1)
        
        n_traces, n_samples = data.shape
        
        # Compute magnitude spectra
        magnitude_data = []
        
        for i in range(0, n_traces, chunk_size):
            end_idx = min(i + chunk_size, n_traces)
            chunk_data = data[i:end_idx]
            
            chunk_magnitudes = []
            for trace in chunk_data:
                # Remove mean and apply windowing
                trace = trace - np.mean(trace)
                window = np.hanning(len(trace))
                trace_windowed = trace * window
                
                # Compute FFT
                fft_trace = fft.fft(trace_windowed)
                
                # Take positive frequencies
                n_pos = len(fft_trace) // 2
                fft_trace = fft_trace[:n_pos]
                
                # Apply frequency limit if specified
                if fmax is not None:
                    freq_array = fft.fftfreq(len(trace_windowed), dt)[:n_pos]
                    freq_mask = freq_array <= fmax
                    fft_trace = fft_trace[freq_mask]
                
                # Compute magnitude
                magnitude = np.abs(fft_trace)
                chunk_magnitudes.append(magnitude)
            
            magnitude_data.extend(chunk_magnitudes)
        
        # Convert to numpy array
        magnitude_data = np.array(magnitude_data)
        
        logging.info(f"CPU 2D spectral descriptor with magnitude computed for {n_traces} traces")
        
        return frequencies, spectral_descriptors, magnitude_data
        
    except Exception as e:
        logging.error(f"Error in CPU 2D spectral descriptor with magnitude calculation: {e}")
        raise


def test_cpu_implementation():
    """
    Test function to verify CPU implementation works correctly.
    """
    print("Testing CPU spectral descriptor implementations...")
    
    # Generate test data
    dt = 0.001  # 1ms sampling
    t = np.arange(0, 1, dt)  # 1 second of data
    
    # Create a test signal with multiple frequencies
    signal = (np.sin(2 * np.pi * 10 * t) + 
              0.5 * np.sin(2 * np.pi * 25 * t) + 
              0.3 * np.sin(2 * np.pi * 50 * t))
    
    # Add some noise
    signal += 0.1 * np.random.randn(len(signal))
    
    try:
        # Test single trace
        print("Testing single trace...")
        freq, spec = dlogst_spec_descriptor_cpu(signal, dt, fmax=100)
        print(f"Single trace: frequencies shape {freq.shape}, spectrum shape {spec.shape}")
        
        # Test 2D chunked
        print("Testing 2D chunked...")
        data_2d = np.array([signal, signal * 0.8, signal * 1.2])  # 3 traces
        freq_2d, spec_2d = dlogst_spec_descriptor_cpu_2d_chunked(data_2d, dt, fmax=100)
        print(f"2D chunked: frequencies shape {freq_2d.shape}, spectrum shape {spec_2d.shape}")
        
        # Test 2D chunked with magnitude
        print("Testing 2D chunked with magnitude...")
        freq_mag, spec_mag, mag_data = dlogst_spec_descriptor_cpu_2d_chunked_mag(data_2d, dt, fmax=100)
        print(f"2D magnitude: frequencies shape {freq_mag.shape}, spectrum shape {spec_mag.shape}, magnitude shape {mag_data.shape}")
        
        print("All CPU implementations working correctly!")
        return True
        
    except Exception as e:
        print(f"Error in CPU implementation test: {e}")
        return False


if __name__ == "__main__":
    # Run tests when executed directly
    test_cpu_implementation()
