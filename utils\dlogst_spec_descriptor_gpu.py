# -*- coding: utf-8 -*-
"""
Merged optimized CUDA version of spectral descriptor calculations using CuPy.
Contains both the 1D and batched 2D implementations:
- dlogst_spec_descriptor_gpu: optimized CUDA version for 1D signals
- dlogst_spec_descriptor_gpu_2d_chunked: batched 2D signals version

Precision optimized to use float32/complex64.
Added Pinned Memory for CPU->GPU transfers.

Merged on April 19, 2025 by devri.agustianto
Modified for float32 precision on April 21, 2025.
Modified for Pinned Memory on April 21, 2025.
"""

import cupy as cp
import numpy as np

# Define a small epsilon suitable for float32 comparisons
EPSILON_F32 = cp.float32(1e-6)

# Get pinned memory pool instance
pinned_memory_pool = cp.cuda.PinnedMemoryPool()
cp.cuda.set_pinned_memory_allocator(pinned_memory_pool.malloc)

# 1D helper functions (modified for float32) - No changes from previous version
def calculate_slope(freq, signal, den_slope):
    """
    Helper function to calculate slope for any signal type using float32.
    Assumes freq, signal, den_slope are CuPy arrays (float32).
    """
    mean_freq = cp.mean(freq, dtype=cp.float32)
    signal_sum = cp.sum(freq[:, cp.newaxis].astype(cp.float32) * signal.astype(cp.float32), axis=0, dtype=cp.float32)
    signal_total_sum = cp.sum(signal, axis=0, dtype=cp.float32)
    return cp.where(cp.abs(den_slope) > EPSILON_F32,
                    (signal_sum - mean_freq * signal_total_sum) / den_slope,
                    cp.float32(0.0))

def calculate_decrease(signal):
    """
    Helper function to calculate spectral decrease using float32.
    Assumes signal is a CuPy array (float32).
    """
    total_sum = cp.sum(signal, axis=0, dtype=cp.float32)
    if signal.shape[0] <= 1:
         return cp.zeros_like(total_sum, dtype=cp.float32)
    k_vec = cp.arange(1, signal.shape[0], dtype=cp.float32)
    weighted = (signal[1:] - signal[0:1]) / k_vec[:, cp.newaxis]
    sum_weighted = cp.sum(weighted, axis=0, dtype=cp.float32)
    return cp.where(total_sum > EPSILON_F32, sum_weighted / total_sum, cp.float32(0.0))

# --- 1D Function (Optimized for float32) ---
# No structural changes needed here for pinned memory as input is usually single trace
def dlogst_spec_descriptor_gpu(data, dt, fmax=None, shape=None, kmax=None, int_val=None,
                               b1=None, b2=None, p_bandwidth=2, roll_percent=0.85, use_band_limited=False):
    """
    Compute spectral descriptors from a 1D signal using GPU (CuPy) with float32 precision.
    """
    # (Code identical to the previous float32 version)
    # ... (rest of the 1D function code from previous version) ...
    # Default parameter handling
    data = np.asarray(data, dtype=np.float32).flatten()
    LS = len(data)
    dt = np.float32(dt) # Ensure dt is float32

    if fmax is None: fmax = LS // 2
    if shape is None: shape = np.float32(0.05)
    if kmax is None: kmax = np.float32(0.25 * (1 / (2 * dt)))
    if int_val is None: int_val = np.float32(0.1 * kmax)
    if b1 is None: b1 = np.float32(0.0)
    if b2 is None: b2 = np.float32(fmax)

    # Move data to GPU
    data_gpu = cp.asarray(data, dtype=cp.float32) # Pinned memory less critical for single transfer
    fsamp = 1 / dt # Will be float32

    # Frequency axis setup
    fmin = 0
    maxf = LS // 2
    freqsamplingrate = 1
    spe_nelements = int(np.ceil((maxf - fmin + 1) / freqsamplingrate))
    freqst_gpu = (fmin + cp.arange(spe_nelements, dtype=cp.float32) * freqsamplingrate) / (dt * LS)
    idf = cp.argmin(cp.abs(freqst_gpu - np.float32(fmax)))
    freqst_gpu = freqst_gpu[:idf + 1]
    N2 = len(freqst_gpu)

    # FFT of input data
    t_gpu = cp.concatenate((cp.arange(1, LS // 2 + 1, dtype=cp.float32), cp.arange(-LS // 2 + 1, 1, dtype=cp.float32))) / LS
    Fd_gpu = cp.fft.fft(data_gpu)
    CFd_gpu = cp.concatenate((Fd_gpu, Fd_gpu)) # complex64

    # Time-frequency representation setup
    time_gpu = cp.arange(LS, dtype=cp.float32) * dt
    kdiv = (kmax / int_val) - 1
    logit = kmax / (1 + kdiv * cp.exp(-shape * freqst_gpu))
    stdq_gpu = 1 / logit # float32
    stdq2_gpu = stdq_gpu ** 2 # float32

    # Precompute Gaussian windows on GPU
    t2_gpu = t_gpu ** 2 # float32
    if N2 <= 1: # Handle edge case where only DC component exists
        GWB_all_gpu = cp.empty((0, LS), dtype=cp.complex64)
    else:
        std_term_gpu = 1 / (cp.sqrt(np.float32(2 * np.pi)) * stdq_gpu[1:]) # float32
        windows_gpu = cp.exp(-t2_gpu[cp.newaxis, :] / (2 * stdq2_gpu[1:, cp.newaxis])) * std_term_gpu[:, cp.newaxis] # float32
        window_sums = cp.sum(windows_gpu, axis=1, keepdims=True, dtype=cp.float32)
        windows_gpu /= cp.where(window_sums < EPSILON_F32, cp.float32(1.0), window_sums)
        GWB_all_gpu = cp.fft.fft(windows_gpu, axis=1)

    # Compute full MST_gpu
    MST_gpu = cp.zeros((N2, LS), dtype=cp.complex64)
    for i in range(1, N2):
        MST_gpu[i, :] = cp.fft.ifft(CFd_gpu[i:i + LS] * GWB_all_gpu[i-1, :])
    st0 = cp.mean(data_gpu, dtype=cp.float32) * cp.ones(LS, dtype=cp.float32)
    MST_gpu[0, :] = st0.astype(cp.complex64)

    # Compute mag, voice, and mag_voice
    mag_gpu = cp.abs(MST_gpu) # float32
    fasa_gpu = cp.angle(MST_gpu) # float32
    wtt_gpu = cp.outer((np.float32(2 * np.pi) * freqst_gpu), time_gpu) # float32
    vc_lst_gpu = mag_gpu * cp.exp(1j * fasa_gpu).astype(cp.complex64) # complex64
    vbal_gpu = vc_lst_gpu * cp.exp(1j * wtt_gpu).astype(cp.complex64) # complex64
    voice_gpu = cp.real(vbal_gpu) # float32
    voice_gpu[0, :] = st0 # float32
    mag_voice_gpu = mag_gpu * voice_gpu # float32

    # Accumulators for spectral descriptors
    total_sum_mag = cp.sum(mag_gpu, axis=0, dtype=cp.float32)
    sum_freq_mag = cp.sum(freqst_gpu[:, cp.newaxis] * mag_gpu, axis=0, dtype=cp.float32)
    indices = cp.argmax(mag_gpu, axis=0)
    if indices.size > 0:
        current_max_freq = freqst_gpu[indices] # float32
    else:
        current_max_freq = cp.zeros_like(total_sum_mag, dtype=cp.float32)

    mag_p = mag_gpu ** 2 # float32
    sum_freq_mag_p = cp.sum(freqst_gpu[:, cp.newaxis] * mag_p, axis=0, dtype=cp.float32)
    sum_mag_p = cp.sum(mag_p, axis=0, dtype=cp.float32)

    # Calculate spectral descriptors
    spec_centroid = cp.where(total_sum_mag > EPSILON_F32, sum_freq_mag / total_sum_mag, cp.float32(0.0))
    fdom = cp.where(sum_mag_p > EPSILON_F32, sum_freq_mag_p / sum_mag_p, cp.float32(0.0))
    mean_freq = cp.mean(freqst_gpu, dtype=cp.float32)
    freq_diff = freqst_gpu - mean_freq # float32
    den_slope = cp.sum(freq_diff ** 2, dtype=cp.float32)

    # Calculate slopes using float32 helper
    mag_slope = calculate_slope(freqst_gpu, mag_gpu, den_slope)
    voice_slope = calculate_slope(freqst_gpu, voice_gpu, den_slope)
    mag_voice_slope = calculate_slope(freqst_gpu, mag_voice_gpu, den_slope)

    # Calculate decrease using float32 helper
    spec_decrease = calculate_decrease(mag_gpu)

    peak_freq = current_max_freq # float32
    norm_peak_freq = peak_freq * (np.float32(2 * np.pi) / fsamp) # float32
    norm_spec_centroid = spec_centroid * (np.float32(2 * np.pi) / fsamp) # float32
    norm_fdom = fdom * (np.float32(2 * np.pi) / fsamp) # float32
    hfc = sum_freq_mag # float32

    # Spectral bandwidth and rolloff
    deviation = cp.abs(freqst_gpu[:, cp.newaxis] - spec_centroid[cp.newaxis, :]) # float32
    bandwidth_sum = cp.sum(mag_gpu * (deviation ** p_bandwidth), axis=0, dtype=cp.float32)
    spec_bandwidth = cp.where(total_sum_mag > EPSILON_F32,
                              (bandwidth_sum / total_sum_mag) ** (np.float32(1.0 / p_bandwidth)), cp.float32(0.0))

    threshold = np.float32(roll_percent) * total_sum_mag # float32
    cum_sum = cp.cumsum(mag_gpu, axis=0, dtype=cp.float32)
    spec_rolloff = cp.full(LS, np.float32(-1.0), dtype=cp.float32)
    if N2 > 0:
        for i in range(N2):
            mask = (cum_sum[i, :] >= threshold) & (spec_rolloff == np.float32(-1.0))
            spec_rolloff = cp.where(mask, freqst_gpu[i], spec_rolloff)
    else:
        spec_rolloff[:] = np.float32(0.0)


    # Determine frequency range for band-limited calculations
    band_idx = cp.where((freqst_gpu >= np.float32(b1)) & (freqst_gpu <= np.float32(b2)))[0]
    use_band = use_band_limited and len(band_idx) >= 2

    # Calculate band-limited slopes and decreases if requested
    mag_slope_band = None
    spec_decrease_band = None
    if len(band_idx) >= 2:
        band_freqs = freqst_gpu[band_idx] # float32
        band_den = cp.sum((band_freqs - cp.mean(band_freqs, dtype=cp.float32)) ** 2, dtype=cp.float32)
        band_mag = mag_gpu[band_idx] # float32
        mag_slope_band = calculate_slope(band_freqs, band_mag, band_den)
        spec_decrease_band = calculate_decrease(band_mag)
        if use_band: # Overwrite full-spectrum results if use_band_limited is True
             band_voice = voice_gpu[band_idx] # float32
             band_mag_voice = mag_voice_gpu[band_idx] # float32
             mag_slope = mag_slope_band
             voice_slope = calculate_slope(band_freqs, band_voice, band_den)
             mag_voice_slope = calculate_slope(band_freqs, band_mag_voice, band_den)
             spec_decrease = spec_decrease_band

    # Transfer results to CPU (as numpy float32 arrays)
    results = {
        'data': data, # Original input data (already float32)
        'peak_freq': cp.asnumpy(peak_freq),
        'spec_centroid': cp.asnumpy(spec_centroid),
        'fdom': cp.asnumpy(fdom),
        'spec_slope': cp.asnumpy(mag_slope),
        'mag_voice_slope': cp.asnumpy(mag_voice_slope),
        'voice_slope': cp.asnumpy(voice_slope),
        'spec_decrease': cp.asnumpy(spec_decrease),
        'time': cp.asnumpy(time_gpu),
        'freqst': cp.asnumpy(freqst_gpu),
        'norm_peak_freq': cp.asnumpy(norm_peak_freq),
        'norm_spec_centroid': cp.asnumpy(norm_spec_centroid),
        'norm_fdom': cp.asnumpy(norm_fdom),
        'hfc': cp.asnumpy(hfc),
        'spec_bandwidth': cp.asnumpy(spec_bandwidth),
        'spec_rolloff': cp.asnumpy(spec_rolloff),
        'mag': cp.asnumpy(mag_gpu),
        'mag_voice': cp.asnumpy(mag_voice_gpu)
    }
    if mag_slope_band is not None:
        results['spec_slope_band'] = cp.asnumpy(mag_slope_band)
    if spec_decrease_band is not None:
        results['spec_decrease_band'] = cp.asnumpy(spec_decrease_band)

    # Clear GPU memory explicitly (optional, helps if run multiple times)
    del data_gpu, Fd_gpu, CFd_gpu, MST_gpu, mag_gpu, fasa_gpu, voice_gpu, mag_voice_gpu
    del freqst_gpu, t_gpu, time_gpu, GWB_all_gpu # etc.
    cp.get_default_memory_pool().free_all_blocks()

    return results


# --- 2D Batched Function (Optimized for float32 + Pinned Memory) ---
def dlogst_spec_descriptor_gpu_2d_chunked(data, dt, fmax=None, shape=None, kmax=None, int_val=None,
                                          b1=None, b2=None, p_bandwidth=2, roll_percent=0.85,
                                          batch_size=50, use_band_limited=False):
    """
    Compute spectral descriptors from a 2D array using GPU (CuPy) with float32 precision,
    processed in batches, using pinned memory for transfers.
    """
    data = np.asarray(data, dtype=np.float32)
    if data.ndim != 2:
        raise ValueError("Input data must be a 2D array with shape (n_signals, signal_length)")

    n_signals, LS = data.shape
    dt = np.float32(dt)

    # Set defaults (ensure float32)
    if fmax is None: fmax = LS // 2
    if shape is None: shape = np.float32(0.05)
    if kmax is None: kmax = np.float32(0.25 * (1 / (2 * dt)))
    if int_val is None: int_val = np.float32(0.1 * kmax)
    if b1 is None: b1 = np.float32(0.0)
    if b2 is None: b2 = np.float32(fmax)

    fsamp = 1 / dt # float32
    fmin = 0
    maxf = LS // 2
    freqsamplingrate = 1

    # Frequency axis (CPU version, used for setup and final output)
    spe_nelements = int(np.ceil((maxf - fmin + 1) / freqsamplingrate))
    freqst = (fmin + np.arange(spe_nelements, dtype=np.float32) * freqsamplingrate) / (dt * LS)
    idf = np.argmin(np.abs(freqst - np.float32(fmax)))
    freqst = freqst[:idf+1]
    N2 = len(freqst)

    # Time axis (CPU version, used for setup and final output)
    t = np.concatenate((np.arange(1, LS//2 + 1, dtype=np.float32), np.arange(-LS//2 + 1, 1, dtype=np.float32))) / LS
    time_vec = np.arange(LS, dtype=np.float32) * dt

    # --- GPU constants ---
    freqst_gpu_const = cp.asarray(freqst, dtype=cp.float32)
    time_gpu = cp.asarray(time_vec, dtype=cp.float32)

    # --- Gaussian window parameters ---
    kdiv = (kmax / int_val) - 1
    logit = kmax / (1 + kdiv * cp.exp(-shape * freqst_gpu_const))
    stdq = 1 / logit
    stdq2 = stdq ** 2

    # Gaussian windows in time domain
    t2 = t ** 2
    t2_gpu = cp.asarray(t2, dtype=cp.float32)
    stdq_gpu = cp.asarray(stdq, dtype=cp.float32)
    stdq2_gpu = cp.asarray(stdq2, dtype=cp.float32)

    if N2 <= 1:
        GWB_all_gpu = cp.empty((0, LS), dtype=cp.complex64)
    else:
        std_term = 1 / (np.sqrt(np.float32(2 * np.pi)) * stdq[1:].get())
        std_term_gpu = cp.asarray(std_term, dtype=cp.float32)
        windows_gpu = cp.exp(-t2_gpu[cp.newaxis, :] / (2 * stdq2_gpu[1:, cp.newaxis])) * std_term_gpu[:, cp.newaxis]
        window_sums = cp.sum(windows_gpu, axis=1, keepdims=True, dtype=cp.float32)
        windows_gpu /= cp.where(window_sums < EPSILON_F32, cp.float32(1.0), window_sums)
        GWB_all_gpu = cp.fft.fft(windows_gpu, axis=1)

    # --- Prepare output containers ---
    descriptor_keys = [
        'peak_freq', 'spec_centroid', 'fdom',
        'spec_slope', 'mag_voice_slope', 'voice_slope',
        'spec_decrease', 'norm_peak_freq', 'norm_spec_centroid',
        'norm_fdom', 'hfc', 'spec_bandwidth', 'spec_rolloff'
    ]
    results_acc = {key: [] for key in descriptor_keys}

    # Determine frequency range for slope calculations
    if use_band_limited and b1 is not None and b2 is not None:
        band_idx = cp.where((freqst_gpu_const >= np.float32(b1)) & (freqst_gpu_const <= np.float32(b2)))[0]
        use_band = len(band_idx) >= 2
    else:
        use_band = False
        band_idx = None

    # --- Slope calculation helper (nested for scope, adapted for float32) ---
    def compute_slope_2d(data_gpu_batch, freqs_gpu):
        """Computes slope for a batch of 2D data [batch, freq, time]"""
        if use_band and band_idx is not None and len(band_idx) >= 2:
            data_sub = data_gpu_batch[:, band_idx, :]
            freq_sub = freqs_gpu[band_idx]
            mf = cp.mean(freq_sub, dtype=cp.float32)
            ms = cp.mean(data_sub, axis=1, keepdims=True, dtype=cp.float32)
            freq_diff = freq_sub - mf
            data_diff = data_sub - ms
            num = cp.sum(freq_diff.reshape(1, -1, 1) * data_diff, axis=1, dtype=cp.float32)
            den = cp.sum(freq_diff ** 2, dtype=cp.float32)
            return cp.where(cp.abs(den) > EPSILON_F32, num / den, cp.zeros_like(num, dtype=cp.float32))
        else:
            mf = cp.mean(freqs_gpu, dtype=cp.float32)
            ms = cp.mean(data_gpu_batch, axis=1, keepdims=True, dtype=cp.float32)
            freq_diff = freqs_gpu - mf
            data_diff = data_gpu_batch - ms
            num = cp.sum(freq_diff.reshape(1, -1, 1) * data_diff, axis=1, dtype=cp.float32)
            den = cp.sum(freq_diff ** 2, dtype=cp.float32)
            return cp.where(cp.abs(den) > EPSILON_F32, num / den, cp.zeros_like(num, dtype=cp.float32))

    # --- Main loop over batches ---
    for start in range(0, n_signals, batch_size):
        end = min(start + batch_size, n_signals)
        data_chunk_np = data[start:end] # Get slice from original numpy array
        n_chunk = data_chunk_np.shape[0]

        # OPTIMIZATION: Use pinned memory for faster H2D transfer
        # cp.asarray will use the pinned_memory_pool automatically
        # because cp.cuda.set_pinned_memory_allocator was called earlier.
        data_gpu = cp.asarray(data_chunk_np)

        # --- Start GPU Computations ---
        Fd_gpu = cp.fft.fft(data_gpu, axis=1)
        CFd_gpu = cp.concatenate((Fd_gpu, Fd_gpu), axis=1)

        MST_gpu = cp.zeros((n_chunk, N2, LS), dtype=cp.complex64)
        if N2 > 1:
            for i in range(1, N2):
                MST_gpu[:, i, :] = cp.fft.ifft(CFd_gpu[:, i:i+LS] * GWB_all_gpu[i-1, cp.newaxis, :].astype(cp.complex64), axis=1)
        st0 = cp.mean(data_gpu, axis=1, keepdims=True, dtype=cp.float32) * cp.ones((1, LS), dtype=cp.float32)
        MST_gpu[:, 0, :] = st0.astype(cp.complex64)

        mag_gpu = cp.abs(MST_gpu)
        fasa_gpu = cp.angle(MST_gpu)
        wtt_gpu = cp.outer((np.float32(2 * np.pi) * freqst_gpu_const), time_gpu)
        vc_lst_gpu = mag_gpu * cp.exp(1j * fasa_gpu).astype(cp.complex64)
        vbal_gpu = vc_lst_gpu * cp.exp(1j * wtt_gpu[cp.newaxis, :, :]).astype(cp.complex64)
        voice_gpu = cp.real(vbal_gpu)
        voice_gpu[:, 0, :] = st0
        mag_voice_gpu = mag_gpu * voice_gpu

        # --- Descriptor calculations (float32) ---
        total_sum_mag = cp.sum(mag_gpu, axis=1, dtype=cp.float32)
        sum_freq_mag = cp.sum(freqst_gpu_const[cp.newaxis, :, cp.newaxis] * mag_gpu, axis=1, dtype=cp.float32)
        indices = cp.argmax(mag_gpu, axis=1)
        indices = cp.clip(indices, 0, N2-1)
        flat_indices = indices.flatten()
        if freqst_gpu_const.size > 0:
             gathered_freqs = cp.take(freqst_gpu_const, flat_indices)
             current_max_freq = gathered_freqs.reshape(n_chunk, LS)
        else:
             current_max_freq = cp.zeros((n_chunk, LS), dtype=cp.float32)

        mag_p = mag_gpu ** 2
        sum_freq_mag_p = cp.sum(freqst_gpu_const[cp.newaxis, :, cp.newaxis] * mag_p, axis=1, dtype=cp.float32)
        sum_mag_p = cp.sum(mag_p, axis=1, dtype=cp.float32)

        # Calculate Spectral Decrease (with band-limited option)
        if use_band and band_idx is not None and len(band_idx) > 1:
            mag_gpu_band = mag_gpu[:, band_idx, :]
            N2_band = len(band_idx)
            if N2_band > 1:
                total_sum_mag_band = cp.sum(mag_gpu_band, axis=1, dtype=cp.float32)
                k_vec_band = cp.arange(1, N2_band, dtype=cp.float32)
                weighted_mag_band = (mag_gpu_band[:, 1:, :] - mag_gpu_band[:, 0:1, :]) / k_vec_band[cp.newaxis, :, cp.newaxis]
                sum_weighted_mag_band = cp.sum(weighted_mag_band, axis=1, dtype=cp.float32)
                spec_decrease = cp.where(total_sum_mag_band > EPSILON_F32, sum_weighted_mag_band / total_sum_mag_band, cp.zeros_like(total_sum_mag_band, dtype=cp.float32))
            else:
                spec_decrease = cp.zeros_like(total_sum_mag, dtype=cp.float32) # Use total_sum_mag shape for consistency
        else: # Full spectrum calculation (original logic)
            if N2 > 1:
                k_vec = cp.arange(1, N2, dtype=cp.float32)
                weighted_mag = (mag_gpu[:, 1:, :] - mag_gpu[:, 0:1, :]) / k_vec[cp.newaxis, :, cp.newaxis]
                sum_weighted_mag = cp.sum(weighted_mag, axis=1, dtype=cp.float32)
                spec_decrease = cp.where(total_sum_mag > EPSILON_F32, sum_weighted_mag / total_sum_mag, cp.zeros_like(total_sum_mag, dtype=cp.float32))
            else:
                spec_decrease = cp.zeros_like(total_sum_mag, dtype=cp.float32)

        spec_centroid = cp.where(total_sum_mag > EPSILON_F32, sum_freq_mag / total_sum_mag, cp.zeros_like(total_sum_mag, dtype=cp.float32))
        fdom = cp.where(sum_mag_p > EPSILON_F32, sum_freq_mag_p / sum_mag_p, cp.zeros_like(total_sum_mag, dtype=cp.float32))

        spec_slope = compute_slope_2d(mag_gpu, freqst_gpu_const)
        voice_slope = compute_slope_2d(voice_gpu, freqst_gpu_const)
        mag_voice_slope = compute_slope_2d(mag_voice_gpu, freqst_gpu_const)

        norm_peak_freq = current_max_freq * (np.float32(2 * np.pi) / fsamp)
        norm_spec_centroid = spec_centroid * (np.float32(2 * np.pi) / fsamp)
        norm_fdom = fdom * (np.float32(2 * np.pi) / fsamp)
        hfc = sum_freq_mag

        deviation = cp.abs(freqst_gpu_const[cp.newaxis, :, cp.newaxis] - spec_centroid[:, cp.newaxis, :])
        bandwidth_sum = cp.sum(mag_gpu * (deviation ** p_bandwidth), axis=1, dtype=cp.float32)
        spec_bandwidth = cp.where(total_sum_mag > EPSILON_F32, (bandwidth_sum / total_sum_mag) ** (np.float32(1.0 / p_bandwidth)), cp.zeros_like(total_sum_mag, dtype=cp.float32))

        threshold = np.float32(roll_percent) * total_sum_mag
        cum_sum = cp.cumsum(mag_gpu, axis=1, dtype=cp.float32)
        spec_rolloff = cp.full((n_chunk, LS), np.float32(-1.0), dtype=cp.float32)
        if N2 > 0:
             for i in range(N2):
                  mask = (cum_sum[:, i, :] >= threshold) & (spec_rolloff == np.float32(-1.0))
                  spec_rolloff = cp.where(mask, freqst_gpu_const[i], spec_rolloff)
        else:
             spec_rolloff[:] = np.float32(0.0)

        # --- Transfer results for this batch to CPU ---
        batch_results_np = {
            'peak_freq': current_max_freq.get(),
            'spec_centroid': spec_centroid.get(),
            'fdom': fdom.get(),
            'spec_slope': spec_slope.get(),
            'voice_slope': voice_slope.get(),
            'mag_voice_slope': mag_voice_slope.get(),
            'spec_decrease': spec_decrease.get(),
            'norm_peak_freq': norm_peak_freq.get(),
            'norm_spec_centroid': norm_spec_centroid.get(),
            'norm_fdom': norm_fdom.get(),
            'hfc': hfc.get(),
            'spec_bandwidth': spec_bandwidth.get(),
            'spec_rolloff': spec_rolloff.get()
        }

        # Append numpy arrays to accumulator lists
        for key in descriptor_keys:
            results_acc[key].append(batch_results_np[key])

        # Cleanup GPU memory for this batch
        del data_gpu, Fd_gpu, CFd_gpu, MST_gpu, mag_gpu, fasa_gpu, vc_lst_gpu, vbal_gpu, voice_gpu, mag_voice_gpu
        del total_sum_mag, sum_freq_mag, indices, current_max_freq, mag_p, sum_freq_mag_p, sum_mag_p
        del spec_decrease, spec_centroid, fdom, spec_slope, voice_slope, mag_voice_slope
        del norm_peak_freq, norm_spec_centroid, norm_fdom, hfc, deviation, bandwidth_sum, spec_bandwidth
        del threshold, cum_sum, spec_rolloff
        # No need to delete data_chunk_np as it's just a view
        cp.get_default_memory_pool().free_all_blocks()
        # Also clear pinned memory pool periodically if memory pressure is high
        # pinned_memory_pool.free_all_blocks() # Maybe less frequent than default pool

    # --- Aggregate all batches and finalize results ---
    final_results = {key: np.concatenate(results_acc[key], axis=0) for key in descriptor_keys if results_acc[key]}
    # Handle empty case for keys not present if n_signals=0
    for key in descriptor_keys:
        if key not in final_results:
             final_results[key] = np.empty((0, LS), dtype=np.float32)


    final_results['time'] = time_vec
    final_results['freqst'] = freqst
    final_results['data'] = data

    return final_results


# --- 2D Batched Function including Mag/MagVoice Output (Optimized for float32 + Pinned Memory) ---
def dlogst_spec_descriptor_gpu_2d_chunked_mag(data, dt, fmax=None, shape=None, kmax=None, int_val=None,
                                              b1=None, b2=None, p_bandwidth=2, roll_percent=0.85,
                                              batch_size=50, use_band_limited=False):
    """
    Compute spectral descriptors, mag, and mag_voice from a 2D array using GPU (CuPy)
    with float32 precision, processed in batches, using pinned memory for transfers.
    """
    data = np.asarray(data, dtype=np.float32)
    if data.ndim != 2:
        raise ValueError("Input data must be a 2D array with shape (n_signals, signal_length)")

    n_signals, LS = data.shape
    dt = np.float32(dt)

    # Set defaults (ensure float32)
    if fmax is None: fmax = LS // 2
    if shape is None: shape = np.float32(0.05)
    if kmax is None: kmax = np.float32(0.25 * (1 / (2 * dt)))
    if int_val is None: int_val = np.float32(0.1 * kmax)
    if b1 is None: b1 = np.float32(0.0)
    if b2 is None: b2 = np.float32(fmax)

    fsamp = 1 / dt # float32
    fmin = 0
    maxf = LS // 2
    freqsamplingrate = 1

    # Frequency axis (CPU version)
    spe_nelements = int(np.ceil((maxf - fmin + 1) / freqsamplingrate))
    freqst = (fmin + np.arange(spe_nelements, dtype=np.float32) * freqsamplingrate) / (dt * LS)
    idf = np.argmin(np.abs(freqst - np.float32(fmax)))
    freqst = freqst[:idf+1]
    N2 = len(freqst)

    # Time axis (CPU version)
    t = np.concatenate((np.arange(1, LS//2 + 1, dtype=np.float32), np.arange(-LS//2 + 1, 1, dtype=np.float32))) / LS
    time_vec = np.arange(LS, dtype=np.float32) * dt

    # --- GPU constants ---
    freqst_gpu_const = cp.asarray(freqst, dtype=cp.float32)
    time_gpu = cp.asarray(time_vec, dtype=cp.float32)

    # --- Gaussian window parameters ---
    kdiv = (kmax / int_val) - 1
    logit = kmax / (1 + kdiv * cp.exp(-shape * freqst_gpu_const))
    stdq = 1 / logit
    stdq2 = stdq ** 2

    # Gaussian windows in time domain
    t2 = t ** 2
    t2_gpu = cp.asarray(t2, dtype=cp.float32)
    stdq_gpu = cp.asarray(stdq, dtype=cp.float32)
    stdq2_gpu = cp.asarray(stdq2, dtype=cp.float32)

    if N2 <= 1:
        GWB_all_gpu = cp.empty((0, LS), dtype=cp.complex64)
    else:
        std_term = 1 / (np.sqrt(np.float32(2 * np.pi)) * stdq[1:].get())
        std_term_gpu = cp.asarray(std_term, dtype=cp.float32)
        windows_gpu = cp.exp(-t2_gpu[cp.newaxis, :] / (2 * stdq2_gpu[1:, cp.newaxis])) * std_term_gpu[:, cp.newaxis]
        window_sums = cp.sum(windows_gpu, axis=1, keepdims=True, dtype=cp.float32)
        windows_gpu /= cp.where(window_sums < EPSILON_F32, cp.float32(1.0), window_sums)
        GWB_all_gpu = cp.fft.fft(windows_gpu, axis=1)

    # --- Prepare output containers ---
    descriptor_keys = [
        'peak_freq', 'spec_centroid', 'fdom',
        'spec_slope', 'mag_voice_slope', 'voice_slope',
        'spec_decrease', 'norm_peak_freq', 'norm_spec_centroid',
        'norm_fdom', 'hfc', 'spec_bandwidth', 'spec_rolloff'
    ]
    results_acc = {key: [] for key in descriptor_keys}
    results_acc['mag'] = []
    results_acc['mag_voice'] = []

    # Determine frequency range for slope calculations
    if use_band_limited and b1 is not None and b2 is not None:
        band_idx = cp.where((freqst_gpu_const >= np.float32(b1)) & (freqst_gpu_const <= np.float32(b2)))[0]
        use_band = len(band_idx) >= 2
    else:
        use_band = False
        band_idx = None

    # --- Slope calculation helper (nested for scope, adapted for float32) ---
    def compute_slope_2d(data_gpu_batch, freqs_gpu):
        """Computes slope for a batch of 2D data [batch, freq, time]"""
        if use_band and band_idx is not None and len(band_idx) >= 2:
            data_sub = data_gpu_batch[:, band_idx, :]
            freq_sub = freqs_gpu[band_idx]
            mf = cp.mean(freq_sub, dtype=cp.float32)
            ms = cp.mean(data_sub, axis=1, keepdims=True, dtype=cp.float32)
            freq_diff = freq_sub - mf
            data_diff = data_sub - ms
            num = cp.sum(freq_diff.reshape(1, -1, 1) * data_diff, axis=1, dtype=cp.float32)
            den = cp.sum(freq_diff ** 2, dtype=cp.float32)
            return cp.where(cp.abs(den) > EPSILON_F32, num / den, cp.zeros_like(num, dtype=cp.float32))
        else:
            mf = cp.mean(freqs_gpu, dtype=cp.float32)
            ms = cp.mean(data_gpu_batch, axis=1, keepdims=True, dtype=cp.float32)
            freq_diff = freqs_gpu - mf
            data_diff = data_gpu_batch - ms
            num = cp.sum(freq_diff.reshape(1, -1, 1) * data_diff, axis=1, dtype=cp.float32)
            den = cp.sum(freq_diff ** 2, dtype=cp.float32)
            return cp.where(cp.abs(den) > EPSILON_F32, num / den, cp.zeros_like(num, dtype=cp.float32))

    # --- Main loop over batches ---
    for start in range(0, n_signals, batch_size):
        end = min(start + batch_size, n_signals)
        data_chunk_np = data[start:end]
        n_chunk = data_chunk_np.shape[0]

        # OPTIMIZATION: Use pinned memory for faster H2D transfer
        # cp.asarray will use the pinned_memory_pool automatically
        # because cp.cuda.set_pinned_memory_allocator was called earlier.
        data_gpu = cp.asarray(data_chunk_np)

        # --- Start GPU Computations ---
        Fd_gpu = cp.fft.fft(data_gpu, axis=1)
        CFd_gpu = cp.concatenate((Fd_gpu, Fd_gpu), axis=1)

        MST_gpu = cp.zeros((n_chunk, N2, LS), dtype=cp.complex64)
        if N2 > 1:
            for i in range(1, N2):
                MST_gpu[:, i, :] = cp.fft.ifft(CFd_gpu[:, i:i+LS] * GWB_all_gpu[i-1, cp.newaxis, :].astype(cp.complex64), axis=1)
        st0 = cp.mean(data_gpu, axis=1, keepdims=True, dtype=cp.float32) * cp.ones((1, LS), dtype=cp.float32)
        MST_gpu[:, 0, :] = st0.astype(cp.complex64)

        mag_gpu = cp.abs(MST_gpu)
        fasa_gpu = cp.angle(MST_gpu)
        wtt_gpu = cp.outer((np.float32(2 * np.pi) * freqst_gpu_const), time_gpu)
        vc_lst_gpu = mag_gpu * cp.exp(1j * fasa_gpu).astype(cp.complex64)
        vbal_gpu = vc_lst_gpu * cp.exp(1j * wtt_gpu[cp.newaxis, :, :]).astype(cp.complex64)
        voice_gpu = cp.real(vbal_gpu)
        voice_gpu[:, 0, :] = st0
        mag_voice_gpu = mag_gpu * voice_gpu

        # --- Store mag and mag_voice for this batch ---
        results_acc['mag'].append(mag_gpu.get())
        results_acc['mag_voice'].append(mag_voice_gpu.get())

        # --- Descriptor calculations (float32) ---
        total_sum_mag = cp.sum(mag_gpu, axis=1, dtype=cp.float32)
        sum_freq_mag = cp.sum(freqst_gpu_const[cp.newaxis, :, cp.newaxis] * mag_gpu, axis=1, dtype=cp.float32)
        indices = cp.argmax(mag_gpu, axis=1)
        indices = cp.clip(indices, 0, N2-1)
        flat_indices = indices.flatten()
        if freqst_gpu_const.size > 0:
             gathered_freqs = cp.take(freqst_gpu_const, flat_indices)
             current_max_freq = gathered_freqs.reshape(n_chunk, LS)
        else:
             current_max_freq = cp.zeros((n_chunk, LS), dtype=cp.float32)

        mag_p = mag_gpu ** 2
        sum_freq_mag_p = cp.sum(freqst_gpu_const[cp.newaxis, :, cp.newaxis] * mag_p, axis=1, dtype=cp.float32)
        sum_mag_p = cp.sum(mag_p, axis=1, dtype=cp.float32)

        # Calculate Spectral Decrease (with band-limited option)
        if use_band and band_idx is not None and len(band_idx) > 1:
            mag_gpu_band = mag_gpu[:, band_idx, :]
            N2_band = len(band_idx)
            if N2_band > 1:
                total_sum_mag_band = cp.sum(mag_gpu_band, axis=1, dtype=cp.float32)
                k_vec_band = cp.arange(1, N2_band, dtype=cp.float32)
                weighted_mag_band = (mag_gpu_band[:, 1:, :] - mag_gpu_band[:, 0:1, :]) / k_vec_band[cp.newaxis, :, cp.newaxis]
                sum_weighted_mag_band = cp.sum(weighted_mag_band, axis=1, dtype=cp.float32)
                spec_decrease = cp.where(total_sum_mag_band > EPSILON_F32, sum_weighted_mag_band / total_sum_mag_band, cp.zeros_like(total_sum_mag_band, dtype=cp.float32))
            else:
                spec_decrease = cp.zeros_like(total_sum_mag, dtype=cp.float32) # Use total_sum_mag shape for consistency
        else: # Full spectrum calculation (original logic)
            if N2 > 1:
                k_vec = cp.arange(1, N2, dtype=cp.float32)
                weighted_mag = (mag_gpu[:, 1:, :] - mag_gpu[:, 0:1, :]) / k_vec[cp.newaxis, :, cp.newaxis]
                sum_weighted_mag = cp.sum(weighted_mag, axis=1, dtype=cp.float32)
                spec_decrease = cp.where(total_sum_mag > EPSILON_F32, sum_weighted_mag / total_sum_mag, cp.zeros_like(total_sum_mag, dtype=cp.float32))
            else:
                spec_decrease = cp.zeros_like(total_sum_mag, dtype=cp.float32)

        spec_centroid = cp.where(total_sum_mag > EPSILON_F32, sum_freq_mag / total_sum_mag, cp.zeros_like(total_sum_mag, dtype=cp.float32))
        fdom = cp.where(sum_mag_p > EPSILON_F32, sum_freq_mag_p / sum_mag_p, cp.zeros_like(total_sum_mag, dtype=cp.float32))

        spec_slope = compute_slope_2d(mag_gpu, freqst_gpu_const)
        voice_slope = compute_slope_2d(voice_gpu, freqst_gpu_const)
        mag_voice_slope = compute_slope_2d(mag_voice_gpu, freqst_gpu_const)

        norm_peak_freq = current_max_freq * (np.float32(2 * np.pi) / fsamp)
        norm_spec_centroid = spec_centroid * (np.float32(2 * np.pi) / fsamp)
        norm_fdom = fdom * (np.float32(2 * np.pi) / fsamp)
        hfc = sum_freq_mag

        deviation = cp.abs(freqst_gpu_const[cp.newaxis, :, cp.newaxis] - spec_centroid[:, cp.newaxis, :])
        bandwidth_sum = cp.sum(mag_gpu * (deviation ** p_bandwidth), axis=1, dtype=cp.float32)
        spec_bandwidth = cp.where(total_sum_mag > EPSILON_F32, (bandwidth_sum / total_sum_mag) ** (np.float32(1.0 / p_bandwidth)), cp.zeros_like(total_sum_mag, dtype=cp.float32))

        threshold = np.float32(roll_percent) * total_sum_mag
        cum_sum = cp.cumsum(mag_gpu, axis=1, dtype=cp.float32)
        spec_rolloff = cp.full((n_chunk, LS), np.float32(-1.0), dtype=cp.float32)
        if N2 > 0:
             for i in range(N2):
                  mask = (cum_sum[:, i, :] >= threshold) & (spec_rolloff == np.float32(-1.0))
                  spec_rolloff = cp.where(mask, freqst_gpu_const[i], spec_rolloff)
        else:
             spec_rolloff[:] = np.float32(0.0)

        # --- Transfer descriptor results for this batch to CPU ---
        batch_results_np = {
            'peak_freq': current_max_freq.get(),
            'spec_centroid': spec_centroid.get(),
            'fdom': fdom.get(),
            'spec_slope': spec_slope.get(),
            'voice_slope': voice_slope.get(),
            'mag_voice_slope': mag_voice_slope.get(),
            'spec_decrease': spec_decrease.get(),
            'norm_peak_freq': norm_peak_freq.get(),
            'norm_spec_centroid': norm_spec_centroid.get(),
            'norm_fdom': norm_fdom.get(),
            'hfc': hfc.get(),
            'spec_bandwidth': spec_bandwidth.get(),
            'spec_rolloff': spec_rolloff.get()
        }

        # Append numpy arrays to accumulator lists
        for key in descriptor_keys:
            results_acc[key].append(batch_results_np[key])

        # Cleanup GPU memory for this batch
        del data_gpu, Fd_gpu, CFd_gpu, MST_gpu, mag_gpu, fasa_gpu, vc_lst_gpu, vbal_gpu, voice_gpu, mag_voice_gpu
        del total_sum_mag, sum_freq_mag, indices, current_max_freq, mag_p, sum_freq_mag_p, sum_mag_p
        del spec_decrease, spec_centroid, fdom, spec_slope, voice_slope, mag_voice_slope
        del norm_peak_freq, norm_spec_centroid, norm_fdom, hfc, deviation, bandwidth_sum, spec_bandwidth
        del threshold, cum_sum, spec_rolloff
        cp.get_default_memory_pool().free_all_blocks()
        # pinned_memory_pool.free_all_blocks() # Optional: less frequent clearing

    # --- Aggregate all batches and finalize results ---
    final_results = {}
    for key in descriptor_keys:
         if results_acc[key]:
              final_results[key] = np.concatenate(results_acc[key], axis=0)
         else:
              final_results[key] = np.empty((0, LS), dtype=np.float32)

    if results_acc['mag']:
         final_results['mag'] = np.concatenate(results_acc['mag'], axis=0)
    else:
         final_results['mag'] = np.empty((0, N2, LS), dtype=np.float32)

    if results_acc['mag_voice']:
         final_results['mag_voice'] = np.concatenate(results_acc['mag_voice'], axis=0)
    else:
         final_results['mag_voice'] = np.empty((0, N2, LS), dtype=np.float32)

    final_results['time'] = time_vec
    final_results['freqst'] = freqst
    final_results['data'] = data

    return final_results