import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.io as pio
import numpy as np
import tkinter as tk
from tkinter import filedialog, simpledialog, messagebox
import segyio
import math
import logging
import tempfile
import os
from utils import find_traces_near_polyline
from utils.processing import calculate_woss

def plot_basemap_with_wells(header_loader, well_df=None, segy_path=None, detail_level='medium'):
    """
    Create a basemap visualization of the seismic survey with well locations.
    Optimized for memory usage with configurable detail levels.

    This function generates a Plotly figure showing the seismic survey geometry
    with inline/crossline grid lines and well marker positions. It also displays
    survey statistics such as trace count and inline/crossline ranges.

    Args:
        header_loader: SegyHeaderLoader object containing survey geometry
        well_df: DataFrame containing well marker information (optional)
        segy_path: Path to SEG-Y file for additional metadata (optional)
        detail_level: Level of detail for the basemap ('low', 'medium', 'high')

    Returns:
        Plotly Figure object with the basemap visualization
    """
    # Create base figure
    fig = go.Figure()

    # Set decimation factors based on detail level
    if detail_level == 'low':
        inline_step = 20
        crossline_step = 20
        trace_decimation = 200
    elif detail_level == 'medium':
        inline_step = 10
        crossline_step = 10
        trace_decimation = 100
    else:  # high
        inline_step = 5
        crossline_step = 5
        trace_decimation = 50

    # Calculate total number of traces for statistics
    total_traces = len(header_loader.x_coords)

    # Determine if we should use a heatmap instead of scatter for very large datasets
    use_heatmap = total_traces > 500000

    # Add inline grid lines (decimated for performance)
    unique_inlines = np.unique(header_loader.inlines)
    if len(unique_inlines) > 0:
        # Adjust step based on number of inlines
        adaptive_step = max(inline_step, len(unique_inlines) // 20)
        for il in unique_inlines[::adaptive_step]:
            mask = header_loader.inlines == il
            # Only add if there are points in this inline
            if np.sum(mask) > 0:
                # Further decimate points along the inline
                indices = np.where(mask)[0][::2]  # Take every other point
                fig.add_trace(go.Scatter(
                    x=header_loader.x_coords[indices],
                    y=header_loader.y_coords[indices],
                    mode='lines',
                    line=dict(color='grey', width=0.5),
                    opacity=0.3,
                    showlegend=False,
                    hoverinfo='skip'  # Disable hover for grid lines
                ))

    # Add crossline grid lines (decimated for performance)
    unique_crosslines = np.unique(header_loader.crosslines)
    if len(unique_crosslines) > 0:
        # Adjust step based on number of crosslines
        adaptive_step = max(crossline_step, len(unique_crosslines) // 20)
        for xl in unique_crosslines[::adaptive_step]:
            mask = header_loader.crosslines == xl
            # Only add if there are points in this crossline
            if np.sum(mask) > 0:
                # Further decimate points along the crossline
                indices = np.where(mask)[0][::2]  # Take every other point
                fig.add_trace(go.Scatter(
                    x=header_loader.x_coords[indices],
                    y=header_loader.y_coords[indices],
                    mode='lines',
                    line=dict(color='grey', width=0.5),
                    opacity=0.3,
                    showlegend=False,
                    hoverinfo='skip'  # Disable hover for grid lines
                ))

    # Add trace locations (heavily decimated for performance)
    if not use_heatmap:
        # Use decimation for scatter plot
        step = max(1, total_traces // 10000)  # Limit to ~10k points max
        step = max(step, trace_decimation)  # Use the larger of calculated or preset step

        indices = np.arange(0, total_traces, step)
        fig.add_trace(go.Scatter(
            x=header_loader.x_coords[indices],
            y=header_loader.y_coords[indices],
            mode='markers',
            marker=dict(color='grey', size=1),
            opacity=0.5,
            name=f'Seismic Traces (1/{step} shown)'
        ))
    else:
        # For very large datasets, use a 2D histogram (heatmap) instead
        # Create a 2D histogram of trace locations
        x_range = [np.min(header_loader.x_coords), np.max(header_loader.x_coords)]
        y_range = [np.min(header_loader.y_coords), np.max(header_loader.y_coords)]

        # Determine appropriate bin size (aim for ~100x100 bins)
        x_bins = min(100, int((x_range[1] - x_range[0]) / 100))
        y_bins = min(100, int((y_range[1] - y_range[0]) / 100))

        # Create 2D histogram
        fig.add_trace(go.Histogram2d(
            x=header_loader.x_coords,
            y=header_loader.y_coords,
            colorscale='Greys',
            nbinsx=x_bins,
            nbinsy=y_bins,
            colorbar=dict(title='Trace Density'),
            name='Trace Density'
        ))

    # Add well markers (if provided)
    if well_df is not None and not well_df.empty:
        # Limit the number of well markers to display if there are too many
        max_wells = 50
        if len(well_df) > max_wells:
            logging.info(f"Limiting well display to {max_wells} wells for performance")
            well_df = well_df.head(max_wells)

        for _, row in well_df.iterrows():
            fig.add_trace(go.Scatter(
                x=[row["X"]],
                y=[row["Y"]],
                mode='markers+text',
                marker=dict(color='red', size=8),
                text=f"{row['Well']} ({row['Surface']})",
                textposition="bottom right",
                name=f"{row['Well']} ({row['Surface']})"
            ))

    # Calculate statistics
    inline_min, inline_max = np.min(header_loader.inlines), np.max(header_loader.inlines)
    crossline_min, crossline_max = np.min(header_loader.crosslines), np.max(header_loader.crosslines)

    # Get time range if segy_path is provided
    time_stats = ""
    if segy_path:
        try:
            with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
                dt = segyfile.bin[segyio.BinField.Interval] / 1_000_000  # Convert to seconds
                num_samples = segyfile.samples.size
                time_range = (0, (num_samples - 1) * dt)
                time_stats = f"Time Range: {time_range[0]:.3f}s - {time_range[1]:.3f}s<br>"
        except Exception as e:
            logging.warning(f"Could not read time range from SEG-Y: {e}")

    # Create statistics annotation
    stats_text = (
        f"Survey Statistics:<br>"
        f"Total Traces: {total_traces:,}<br>"
        f"Inline Range: {inline_min} - {inline_max}<br>"
        f"Crossline Range: {crossline_min} - {crossline_max}<br>"
        f"{time_stats}"
    )

    # Add annotation with statistics
    fig.add_annotation(
        x=0.02,
        y=0.98,
        xref="paper",
        yref="paper",
        text=stats_text,
        showarrow=False,
        font=dict(size=12),
        bgcolor="rgba(255, 255, 255, 0.8)",
        bordercolor="black",
        borderwidth=1,
        borderpad=4,
        align="left"
    )

    x_range = np.ptp(header_loader.x_coords)
    y_range = np.ptp(header_loader.y_coords)
    aspect_ratio = x_range / y_range if y_range != 0 else 1

    # Use WebGL renderer for better performance with large datasets
    fig.update_layout(
        title="Seismic Survey Geometry with Well Markers",
        xaxis_title="X Coordinate",
        yaxis_title="Y Coordinate",
        showlegend=True,
        width=800,
        height=600,
        yaxis=dict(scaleanchor="x", scaleratio=1/aspect_ratio)
    )

    # Return the figure instead of showing it (for Streamlit compatibility)
    return fig

def plot_interactive_basemap(header_loader, well_df=None, segy_path=None, highlight_indices=None, polyline_vertices=None, detail_level='medium'):
    """
    Create an interactive basemap that shows trace indices on hover and click.
    Optimized for memory usage with configurable detail levels.

    Args:
        header_loader: The SegyHeaderLoader object
        well_df: DataFrame containing well information
        segy_path: Path to the SEG-Y file
        highlight_indices: List of trace indices to highlight
        polyline_vertices: List of (x,y) tuples for polyline vertices
        detail_level: Level of detail for the basemap ('low', 'medium', 'high')
    """
    # Create the base figure
    fig = go.Figure()

    # Set decimation factors based on detail level
    if detail_level == 'low':
        inline_step = 20
        crossline_step = 20
        target_points = 25000
    elif detail_level == 'medium':
        inline_step = 10
        crossline_step = 10
        target_points = 50000
    else:  # high
        inline_step = 5
        crossline_step = 5
        target_points = 75000

    # Calculate total number of traces for statistics
    total_traces = len(header_loader.x_coords)

    # Add inline grid lines (decimated for performance)
    unique_inlines = np.unique(header_loader.inlines)
    if len(unique_inlines) > 0:
        # Adjust step based on number of inlines
        adaptive_step = max(inline_step, len(unique_inlines) // 20)
        for il in unique_inlines[::adaptive_step]:
            mask = header_loader.inlines == il
            # Only add if there are points in this inline
            if np.sum(mask) > 0:
                # Further decimate points along the inline
                indices = np.where(mask)[0][::2]  # Take every other point
                fig.add_trace(go.Scatter(
                    x=header_loader.x_coords[indices],
                    y=header_loader.y_coords[indices],
                    mode='lines',
                    line=dict(color='grey', width=0.5),
                    opacity=0.2,  # Reduced opacity
                    showlegend=False,
                    hoverinfo='skip'  # Disable hover for grid lines
                ))

    # Add crossline grid lines (decimated for performance)
    unique_crosslines = np.unique(header_loader.crosslines)
    if len(unique_crosslines) > 0:
        # Adjust step based on number of crosslines
        adaptive_step = max(crossline_step, len(unique_crosslines) // 20)
        for xl in unique_crosslines[::adaptive_step]:
            mask = header_loader.crosslines == xl
            # Only add if there are points in this crossline
            if np.sum(mask) > 0:
                # Further decimate points along the crossline
                indices = np.where(mask)[0][::2]  # Take every other point
                fig.add_trace(go.Scatter(
                    x=header_loader.x_coords[indices],
                    y=header_loader.y_coords[indices],
                    mode='lines',
                    line=dict(color='grey', width=0.5),
                    opacity=0.2,  # Reduced opacity
                    showlegend=False,
                    hoverinfo='skip'  # Disable hover for grid lines
                ))

    # --- Add Improved Decimation Logic ---
    num_unique_traces = len(header_loader.x_coords)
    # Aim for a maximum number of points to display for performance
    step = max(1, num_unique_traces // target_points)
    decimated_indices = np.arange(0, num_unique_traces, step)
    logging.info(f"Basemap: Displaying {len(decimated_indices)} out of {num_unique_traces} trace locations (decimation step={step}) for performance.")
    # --- End Decimation Logic ---

    # Create hover text only for decimated points
    hover_text = []
    for i in decimated_indices: # Iterate over decimated indices
        trace_idx = header_loader.unique_indices[i]
        inline = header_loader.inlines[i]
        crossline = header_loader.crosslines[i]
        hover_text.append(f"Trace: {trace_idx}<br>Inline: {inline}<br>Crossline: {crossline}<br>X: {header_loader.x_coords[i]:.2f}<br>Y: {header_loader.y_coords[i]:.2f}") # Added X,Y

    # Add decimated trace points with hover info
    fig.add_trace(go.Scatter(
        x=header_loader.x_coords[decimated_indices], # Use decimated coordinates
        y=header_loader.y_coords[decimated_indices], # Use decimated coordinates
        mode='markers',
        marker=dict(color='blue', size=2), # Smaller markers might be better
        opacity=0.6,
        name=f'Seismic Traces (1/{step} shown)', # Update name
        hoverinfo='text',
        hovertext=hover_text, # Use decimated hover text
        customdata=header_loader.unique_indices[decimated_indices] # Store original indices for decimated points
    ))

    # Highlight specific trace indices if provided
    if highlight_indices and len(highlight_indices) > 0:
        # Find positions of these indices within unique_indices
        highlight_positions = []
        valid_highlight_indices = []

        for idx in highlight_indices:
            positions = np.where(header_loader.unique_indices == idx)[0]
            if len(positions) > 0:
                highlight_positions.append(positions[0])
                valid_highlight_indices.append(idx)

        if highlight_positions:
            highlight_x = [header_loader.x_coords[pos] for pos in highlight_positions]
            highlight_y = [header_loader.y_coords[pos] for pos in highlight_positions]

            # Create hover text for highlighted points
            highlight_hover = [f"Selected Trace: {idx}<br>Inline: {header_loader.inlines[pos]}<br>Crossline: {header_loader.crosslines[pos]}"
                              for idx, pos in zip(valid_highlight_indices, highlight_positions)]

            fig.add_trace(go.Scatter(
                x=highlight_x,
                y=highlight_y,
                mode='markers+text',
                marker=dict(color='green', size=10, symbol='circle-open', line=dict(width=2)),
                text=[str(idx) for idx in valid_highlight_indices],
                textposition="top center",
                textfont=dict(size=10, color="darkgreen"),
                name='Selected Indices',
                hoverinfo='text',
                hovertext=highlight_hover
            ))

    # Add well information if provided
    if well_df is not None and not well_df.empty:
        for _, row in well_df.iterrows():
            fig.add_trace(go.Scatter(
                x=[row["X"]],
                y=[row["Y"]],
                mode='markers+text',
                marker=dict(color='red', size=8),
                text=f"{row['Well']} ({row['Surface']})",
                textposition="bottom right",
                name=f"{row['Well']} ({row['Surface']})"
            ))

    # Calculate statistics
    total_traces = len(header_loader.x_coords)
    inline_min, inline_max = np.min(header_loader.inlines), np.max(header_loader.inlines)
    crossline_min, crossline_max = np.min(header_loader.crosslines), np.max(header_loader.crosslines)

    # Get time range if segy_path is provided
    time_stats = ""
    if segy_path:
        try:
            with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
                dt = segyfile.bin[segyio.BinField.Interval] / 1_000_000  # Convert to seconds
                num_samples = segyfile.samples.size
                time_range = (0, (num_samples - 1) * dt)
                time_stats = f"Time Range: {time_range[0]:.3f}s - {time_range[1]:.3f}s<br>"
        except Exception as e:
            print(f"Could not read time range from SEG-Y: {e}")

    # Create statistics annotation
    stats_text = (
        f"Survey Statistics:<br>"
        f"Total Traces: {total_traces:,}<br>"
        f"Inline Range: {inline_min} - {inline_max}<br>"
        f"Crossline Range: {crossline_min} - {crossline_max}<br>"
        f"{time_stats}"
    )

    # Add polyline coordinates to statistics if provided
    if polyline_vertices and len(polyline_vertices) > 0:
        stats_text += "<br><br>Polyline Vertices:<br>"
        for i, (x, y) in enumerate(polyline_vertices):
            stats_text += f"pt{i+1} x: {x:.2f} y: {y:.2f}<br>"

    # Add annotation with statistics
    fig.add_annotation(
        x=0.02,
        y=0.98,
        xref="paper",
        yref="paper",
        text=stats_text,
        showarrow=False,
        font=dict(size=12),
        bgcolor="rgba(255, 255, 255, 0.8)",
        bordercolor="black",
        borderwidth=1,
        borderpad=4,
        align="left"
    )

    # --- Modify Instructions Annotation ---
    instructions_text = (
        "Interactive Controls:<br>"
        "• Hover over points to see trace info (X, Y, Index)<br>"
        "• Click on points to display trace index<br>"
        "• Use X,Y coordinates to define a polyline (option 5)" # Updated instruction
    )

    fig.add_annotation(
        x=0.02,
        y=0.82,
        xref="paper",
        yref="paper",
        text=instructions_text,
        showarrow=False,
        font=dict(size=12),
        bgcolor="rgba(255, 255, 255, 0.8)",
        bordercolor="black",
        borderwidth=1,
        borderpad=4,
        align="left"
    )
    # --- End Instructions Modification ---

    # Calculate aspect ratio for proper display
    x_range = np.ptp(header_loader.x_coords)
    y_range = np.ptp(header_loader.y_coords)
    aspect_ratio = x_range / y_range if y_range != 0 else 1

    # Set up the layout with customizations
    fig.update_layout(
        title="Interactive Seismic Survey Basemap (Hover/Click for Trace Information)",
        xaxis_title="X Coordinate",
        yaxis_title="Y Coordinate",
        showlegend=True,
        width=900,
        height=700,
        yaxis=dict(scaleanchor="x", scaleratio=1/aspect_ratio),
        # Add a text annotation that will be updated on click
        annotations=[
            dict(
                x=0.5,
                y=0.02,
                xref="paper",
                yref="paper",
                text="Click on a trace point to display its index",
                showarrow=False,
                font=dict(size=14),
                bgcolor="rgba(255, 255, 255, 0.8)",
                bordercolor="black",
                borderwidth=1,
                borderpad=4,
                align="center"
            )
        ],
        # Add click event handling for trace points
        # Note: This part would rely on Plotly's FigureWidget in an interactive environment
        hovermode="closest"
    )

    # Return the figure instead of showing it (for Streamlit compatibility)
    # Note: In Streamlit, config options can be passed directly to st.plotly_chart()
    return fig

def create_polyline_selection_ui(root=None, header_loader=None, well_df_basemap=None, segy_path=None):
    """Create a custom interactive UI for polyline selection"""
    # If root is not provided, create a new Tk instance
    if root is None:
        root = tk.Tk()
        root.withdraw()

    # --- ADD THIS CHECK ---
    if header_loader is None:
        raise ValueError("header_loader must be provided to create_polyline_selection_ui")
    # ----------------------

    polyline_window = tk.Toplevel(root)
    polyline_window.title("Polyline Selection")
    polyline_window.geometry("500x500")

    # Frame for vertices list
    vertices_frame = tk.Frame(polyline_window)
    vertices_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    # Create a listbox to show selected vertices
    tk.Label(vertices_frame, text="Selected Polyline Vertices:").pack(anchor='w')
    vertices_list = tk.Text(vertices_frame, height=10, width=50)
    vertices_list.pack(fill=tk.BOTH, expand=True)

    # Stats display
    stats_var = tk.StringVar(value="Vertices: 0 | Total Length: 0.0 | Selected Traces: 0")
    stats_label = tk.Label(polyline_window, textvariable=stats_var)
    stats_label.pack(pady=5)

    # Control buttons
    button_frame = tk.Frame(polyline_window)
    button_frame.pack(fill=tk.X, padx=10, pady=5)

    vertices = []

    def add_vertex():
        dialog = tk.Toplevel(polyline_window)
        dialog.title("Add Vertex")

        tk.Label(dialog, text="X Coordinate:").grid(row=0, column=0, padx=5, pady=5)
        x_entry = tk.Entry(dialog)
        x_entry.grid(row=0, column=1, padx=5, pady=5)

        tk.Label(dialog, text="Y Coordinate:").grid(row=1, column=0, padx=5, pady=5)
        y_entry = tk.Entry(dialog)
        y_entry.grid(row=1, column=1, padx=5, pady=5)

        def on_ok():
            try:
                x = float(x_entry.get())
                y = float(y_entry.get())
                vertices.append((x, y))
                update_vertices_list()
                update_stats()
                dialog.destroy()
            except ValueError:
                messagebox.showerror("Invalid Input", "Please enter valid numeric coordinates")

        tk.Button(dialog, text="OK", command=on_ok).grid(row=2, column=0, columnspan=2, pady=10)

    def import_vertices():
        file_path = filedialog.askopenfilename(
            title="Select coordinate file",
            filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if not file_path:
            return

        imported_vertices = import_coordinates_from_file(file_path)
        if imported_vertices:
            vertices.clear()
            vertices.extend(imported_vertices)
            update_vertices_list()
            update_stats()
            messagebox.showinfo("Import Successful", f"Imported {len(imported_vertices)} vertices")

    def update_vertices_list():
        vertices_list.delete(1.0, tk.END)
        for i, (x, y) in enumerate(vertices):
            vertices_list.insert(tk.END, f"{i+1}. X: {x:.2f}, Y: {y:.2f}\n")

    def update_stats():
        length = 0
        for i in range(len(vertices) - 1):
            x1, y1 = vertices[i]
            x2, y2 = vertices[i + 1]
            length += math.sqrt((x2-x1)**2 + (y2-y1)**2)

        vertex_info = ""
        if vertices:
            vertex_info = " | Vertices: "
            vertex_info += ", ".join([f"({x:.1f},{y:.1f})" for x, y in vertices[:3]])
            if len(vertices) > 3:
                vertex_info += "..."

        stats_var.set(f"Vertices: {len(vertices)} | Total Length: {length:.2f} | Selected Traces: {len(selected_indices)}{vertex_info}")

    def update_map():
        # Show basemap with current polyline
        plot_interactive_basemap(header_loader, well_df_basemap, segy_path,
                                highlight_indices=selected_indices,
                                polyline_vertices=vertices,
                                detail_level='low')  # Use low detail for better performance

    def calculate_selection():
        nonlocal selected_indices
        if len(vertices) < 2:
            messagebox.showwarning("Warning", "Need at least 2 vertices to define a polyline")
            return

        # Ask for distance tolerance
        tolerance = simpledialog.askfloat("Distance Tolerance",
                                         "Enter maximum distance from polyline:",
                                         minvalue=0.1, initialvalue=10.0)
        if tolerance is None:
            return

        # Find traces near polyline
        selected_indices = find_traces_near_polyline(header_loader, vertices, tolerance)
        update_stats()
        update_map()

        # Update display with count
        messagebox.showinfo("Selection Complete",
                           f"Found {len(selected_indices)} traces within {tolerance} units of polyline")

    # Add buttons
    tk.Button(button_frame, text="Add Vertex", command=add_vertex).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Import from File", command=import_vertices).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Update Map", command=update_map).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Calculate Selection", command=calculate_selection).pack(side=tk.LEFT, padx=5)

    # OK/Cancel buttons
    final_button_frame = tk.Frame(polyline_window)
    final_button_frame.pack(fill=tk.X, padx=10, pady=10)

    def on_ok():
        if len(selected_indices) == 0 and len(vertices) >= 2:
            if messagebox.askyesno("No Selection",
                                  "No traces have been selected yet. Calculate selection now?"):
                calculate_selection()
                if len(selected_indices) > 0:
                    polyline_window.destroy()
            else:
                polyline_window.destroy()
        else:
            polyline_window.destroy()

    def on_cancel():
        nonlocal selected_indices
        selected_indices = []
        polyline_window.destroy()

    tk.Button(final_button_frame, text="OK", command=on_ok).pack(side=tk.LEFT, padx=5)
    tk.Button(final_button_frame, text="Cancel", command=on_cancel).pack(side=tk.RIGHT, padx=5)

    # Initialize
    selected_indices = []
    update_vertices_list()
    update_stats()

    polyline_window.wait_window()
    return vertices, selected_indices

def import_coordinates_from_file(file_path):
    """
    Import X,Y coordinates from various text file formats.
    Supports CSV, space-delimited, and tab-delimited formats.

    Args:
        file_path: Path to the ASCII file

    Returns:
        List of (x,y) coordinate tuples
    """
    coordinates = []

    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()

        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):  # Skip empty lines and comments
                continue

            # Try different delimiters
            if ',' in line:
                parts = line.split(',')
            elif '\t' in line:
                parts = line.split('\t')
            else:
                parts = line.split()

            # Clean and convert to float
            if len(parts) >= 2:
                try:
                    x = float(parts[0].strip())
                    y = float(parts[1].strip())
                    coordinates.append((x, y))
                except ValueError:
                    print(f"Warning: Could not parse line: {line}")

    except Exception as e:
        messagebox.showerror("Import Error", f"Error importing coordinates: {str(e)}")
        return []

    print(f"Successfully imported {len(coordinates)} coordinates from {file_path}")
    return coordinates

# --- Spectral Descriptor Visualization Functions ---

def plot_spectral_descriptors(trace_data, time_vector, descriptors, plot_settings, trace_idx=None, well_marker_name=None):
    """
    Plot spectral descriptors for a single trace in the style of the reference script.

    Args:
        trace_data: The seismic trace data
        time_vector: Time vector for the trace
        descriptors: Dictionary containing spectral descriptors
        plot_settings: Dictionary containing plot settings
        trace_idx: Trace index (optional)
        well_marker_name: Well marker name (optional)

    Returns:
        Plotly figure object
    """
    # Define available outputs
    available_outputs = [
        "Input Signal",
        "Magnitude Spectrogram",
        "Magnitude * Voice",
        "Normalized dominant frequencies",
        "Spectral Slope",
        "Spectral Bandwidth",
        "Spectral Rolloff",
        "Mag*Voice Slope",
        "Spectral Decrease",
        "HFC",
        "WOSS"
    ]

    # Use selected outputs from plot_settings or default to all
    selected_outputs = plot_settings.get('selected_outputs', available_outputs)

    # Create subplot titles
    subplot_titles = []
    for output in selected_outputs:
        if output == "Input Signal":
            subplot_titles.append("Seismic<br>Amplitude")
        elif output == "Magnitude Spectrogram":
            subplot_titles.append("Magnitude<br>Spectrogram")
        elif output == "Magnitude * Voice":
            subplot_titles.append("Magnitude*Voice<br>Spectrogram")
        elif output == "Normalized dominant frequencies":
            subplot_titles.append("Dominant<br>Frequency")
        elif output == "Spectral Slope":
            subplot_titles.append("Spectral<br>Slope")
        elif output == "Spectral Bandwidth":
            subplot_titles.append("Spectral<br>Bandwidth")
        elif output == "Spectral Rolloff":
            subplot_titles.append("Spectral<br>Rolloff")
        elif output == "Mag*Voice Slope":
            subplot_titles.append("Mag*Voice<br>Slope")
        elif output == "Spectral Decrease":
            subplot_titles.append("Spectral<br>Decrease")
        elif output == "HFC":
            subplot_titles.append("High Frequency<br>Content")
        elif output == "WOSS":
            subplot_titles.append("WOSS")

    # Create subplots
    fig = make_subplots(rows=1, cols=len(selected_outputs), subplot_titles=subplot_titles)

    # Get normalization values from plot_settings
    hfc_p95 = plot_settings.get('hfc_p95', 1.0)
    spec_decrease_p95 = plot_settings.get('spec_decrease_p95', 1.0)

    # Normalize descriptors
    hfc_normalized = descriptors.get('hfc', np.array([])) / hfc_p95 if hfc_p95 != 0 else descriptors.get('hfc', np.array([]))
    spec_decrease_normalized = descriptors.get('spec_decrease', np.array([])) / spec_decrease_p95 if spec_decrease_p95 != 0 else descriptors.get('spec_decrease', np.array([]))

    # Calculate initial colorbar position and spacing for spectrograms
    initial_x = 1.01  # Reduced from 1.02 to bring colorbar legends closer to plots
    colorbar_spacing = 0.05  # Reduced from 0.08 to bring colorbar legends closer together

    # Get frequency limits from plot_settings
    freq_min = plot_settings.get('freq_min', 0.0) # Use freq_min from plot_settings
    freq_max = plot_settings.get('freq_max', 125.0) # Use freq_max from plot_settings
    # Create the range list, handle None values
    freq_range = [freq_min, freq_max]

    # Plot each selected output
    col_idx = 1
    for output in selected_outputs:
        if output == "Input Signal":
            fig.add_trace(go.Scatter(x=trace_data, y=time_vector, mode='lines'), row=1, col=col_idx)
            # Try multiple possible key formats for Input Signal range
            input_range = None
            for key in ['Input Signal', 'input_signal']:
                if key in plot_settings:
                    input_range = plot_settings[key]
                    logging.info(f"Found Input Signal range under key '{key}': {input_range}")
                    break
            if input_range is None:
                input_range = [-1, 1]  # Default if not found

            fig.update_xaxes(title_text='Amplitude', row=1, col=col_idx, range=input_range)
            col_idx += 1
        elif output == "Magnitude Spectrogram":
            if 'mag' in descriptors and 'freqst' in descriptors:
                mag = descriptors['mag']
                freqst = descriptors['freqst']
                freq_limit_index = np.argmin(np.abs(freqst - freq_max)) if len(freqst) > 0 else len(freqst)
                cmap_name = plot_settings.get('magnitude_spectrogram_colormap', 'rainbow')
                cmap_min = plot_settings.get('magnitude_spectrogram_cmap_min', 0)
                cmap_max = plot_settings.get('magnitude_spectrogram_cmap_max', 1)

                # Always show colorbar for Magnitude Spectrogram, regardless of plot mode
                fig.add_trace(go.Heatmap(
                    z=mag[:freq_limit_index, :].T,
                    x=freqst[:freq_limit_index],
                    y=time_vector,
                    colorscale=cmap_name,
                    zmin=cmap_min,
                    zmax=cmap_max,
                    colorbar=dict(
                        title=dict(text='Magnitude', side='right', font=dict(size=14)),
                        x=initial_x + (col_idx - 1) * colorbar_spacing,
                        y=0.5, len=0.8, yanchor='middle', xanchor='left', tickfont=dict(size=12),
                        thickness=14
                    )
                ), row=1, col=col_idx)
                fig.update_xaxes(title_text='Frequency (Hz)', row=1, col=col_idx, range=freq_range) # Apply freq_range
            else:
                # Handle missing data
                fig.add_annotation(text="Data unavailable", row=1, col=col_idx, showarrow=False)
                fig.update_xaxes(title_text='Frequency (Hz)', row=1, col=col_idx, range=freq_range) # Still apply freq_range
            col_idx += 1
        elif output == "Magnitude * Voice":
            if 'mag_voice' in descriptors and 'freqst' in descriptors:
                mag_voice = descriptors['mag_voice']
                freqst = descriptors['freqst']
                freq_limit_index = np.argmin(np.abs(freqst - freq_max)) if len(freqst) > 0 else len(freqst)
                cmap_name = plot_settings.get('magnitude_voice_colormap', 'rainbow')
                cmap_min = plot_settings.get('magnitude__voice_cmap_min', -1) # Note key difference
                cmap_max = plot_settings.get('magnitude__voice_cmap_max', 1) # Note key difference

                # Always show colorbar for Magnitude*Voice, regardless of plot mode
                fig.add_trace(go.Heatmap(
                    z=mag_voice[:freq_limit_index, :].T,
                    x=freqst[:freq_limit_index],
                    y=time_vector,
                    colorscale=cmap_name,
                    zmin=cmap_min,
                    zmax=cmap_max,
                    colorbar=dict(
                        title=dict(text='Mag*Voice', side='right', font=dict(size=14)),
                        x=initial_x + (col_idx - 1) * colorbar_spacing,
                        y=0.5, len=0.8, yanchor='middle', xanchor='left', tickfont=dict(size=12),
                        thickness=14
                    )
                ), row=1, col=col_idx)
                fig.update_xaxes(title_text='Frequency (Hz)', row=1, col=col_idx, range=freq_range) # Apply freq_range
            else:
                # Handle missing data
                fig.add_annotation(text="Data unavailable", row=1, col=col_idx, showarrow=False)
                fig.update_xaxes(title_text='Frequency (Hz)', row=1, col=col_idx, range=freq_range) # Still apply freq_range
            col_idx += 1
        elif output == "Normalized dominant frequencies":
            if 'peak_freq' in descriptors: fig.add_trace(go.Scatter(x=descriptors['peak_freq'], y=time_vector, mode='lines', name='Peak Freq'), row=1, col=col_idx)
            if 'spec_centroid' in descriptors: fig.add_trace(go.Scatter(x=descriptors['spec_centroid'], y=time_vector, mode='lines', name='Centroid'), row=1, col=col_idx)
            if 'fdom' in descriptors: fig.add_trace(go.Scatter(x=descriptors['fdom'], y=time_vector, mode='lines', name='Dom Freq'), row=1, col=col_idx)
            # Apply range from plot_settings['Normalized Dominant Frequency'] or freq_range
            norm_dom_freq_range = plot_settings.get('Normalized Dominant Frequency', freq_range)
            fig.update_xaxes(title_text='Frequency (Hz)', row=1, col=col_idx, range=norm_dom_freq_range)
            col_idx += 1
        elif output == "Spectral Slope":
            if 'spec_slope' in descriptors:
                fig.add_trace(go.Scatter(x=descriptors['spec_slope'], y=time_vector, mode='lines'), row=1, col=col_idx)
                # Try multiple possible key formats for Spectral Slope range
                spec_slope_range = None
                for key in ['Spectral Slope', 'spectral_slope']:
                    if key in plot_settings:
                        spec_slope_range = plot_settings[key]
                        logging.info(f"Found Spectral Slope range under key '{key}': {spec_slope_range}")
                        break
                # Also check for cmap min/max keys directly
                if spec_slope_range is None and 'spectral_slope_cmap_min' in plot_settings and 'spectral_slope_cmap_max' in plot_settings:
                    spec_slope_range = [plot_settings['spectral_slope_cmap_min'], plot_settings['spectral_slope_cmap_max']]
                    logging.info(f"Found Spectral Slope range from cmap min/max: {spec_slope_range}")
                if spec_slope_range is None:
                    spec_slope_range = [-1, 1]  # Default if not found

                # Add detailed logging to debug the issue
                logging.info(f"Final Spectral Slope range: {spec_slope_range}")
                logging.info(f"Available plot_settings keys: {list(plot_settings.keys())}")
                if 'spectral_slope_cmap_min' in plot_settings:
                    logging.info(f"spectral_slope_cmap_min value: {plot_settings['spectral_slope_cmap_min']}")
                if 'spectral_slope_cmap_max' in plot_settings:
                    logging.info(f"spectral_slope_cmap_max value: {plot_settings['spectral_slope_cmap_max']}")

                fig.update_xaxes(title_text='Slope', row=1, col=col_idx, range=spec_slope_range)
            else:
                fig.add_annotation(text="Data unavailable", row=1, col=col_idx, showarrow=False)
                fig.update_xaxes(title_text='Slope', row=1, col=col_idx)
            col_idx += 1
        elif output == "Spectral Bandwidth":
            if 'spec_bandwidth' in descriptors:
                fig.add_trace(go.Scatter(x=descriptors['spec_bandwidth'], y=time_vector, mode='lines'), row=1, col=col_idx)
                # Apply range from plot_settings['Spectral Bandwidth'] if available
                spec_bw_range = plot_settings.get('Spectral Bandwidth', [0, 50])
                fig.update_xaxes(title_text='Bandwidth (Hz)', row=1, col=col_idx, range=spec_bw_range)
            else:
                fig.add_annotation(text="Data unavailable", row=1, col=col_idx, showarrow=False)
                fig.update_xaxes(title_text='Bandwidth (Hz)', row=1, col=col_idx)
            col_idx += 1
        elif output == "Spectral Rolloff":
            if 'spec_rolloff' in descriptors:
                fig.add_trace(go.Scatter(x=descriptors['spec_rolloff'], y=time_vector, mode='lines'), row=1, col=col_idx)
                # Apply range from plot_settings['Spectral Rolloff'] if available
                spec_rolloff_range = plot_settings.get('Spectral Rolloff', [0, 50])
                fig.update_xaxes(title_text='Rolloff (Hz)', row=1, col=col_idx, range=spec_rolloff_range)
            else:
                fig.add_annotation(text="Data unavailable", row=1, col=col_idx, showarrow=False)
                fig.update_xaxes(title_text='Rolloff (Hz)', row=1, col=col_idx)
            col_idx += 1
        elif output == "Mag*Voice Slope":
            if 'mag_voice_slope' in descriptors:
                mag_voice_slope = descriptors['mag_voice_slope']
                fig.add_trace(go.Scatter(x=mag_voice_slope, y=time_vector, mode='lines'), row=1, col=col_idx)

                # Enhanced logging to debug the issue
                logging.info(f"Processing Mag*Voice Slope in plot_spectral_descriptors")
                logging.info(f"Available keys in plot_settings: {list(plot_settings.keys())}")

                # Try multiple possible key formats for Mag*Voice Slope limits
                if 'Mag*Voice Slope' in plot_settings and isinstance(plot_settings['Mag*Voice Slope'], list) and len(plot_settings['Mag*Voice Slope']) == 2:
                    # Use the range passed as a list under 'Mag*Voice Slope' key
                    mv_slope_range = plot_settings['Mag*Voice Slope']
                    logging.info(f"Using Mag*Voice Slope limits from 'Mag*Voice Slope' key: {mv_slope_range}")
                elif 'mag_voice_slope' in plot_settings and isinstance(plot_settings['mag_voice_slope'], list) and len(plot_settings['mag_voice_slope']) == 2:
                    # Use the range passed as a list under 'mag_voice_slope' key
                    mv_slope_range = plot_settings['mag_voice_slope']
                    logging.info(f"Using Mag*Voice Slope limits from 'mag_voice_slope' key: {mv_slope_range}")
                elif 'mag_voice_slope_cmap_min' in plot_settings and 'mag_voice_slope_cmap_max' in plot_settings:
                    # Use individual min/max values
                    mv_slope_range = [plot_settings['mag_voice_slope_cmap_min'], plot_settings['mag_voice_slope_cmap_max']]
                    logging.info(f"Using Mag*Voice Slope limits from individual min/max keys: {mv_slope_range}")
                else:
                    # Calculate p5 and p95 from the current data if available
                    try:
                        p5 = np.percentile(mag_voice_slope, 5)
                        p95 = np.percentile(mag_voice_slope, 95)
                        # Use symmetric limits based on the larger absolute value
                        abs_max = max(abs(p5), abs(p95))
                        mv_slope_range = [-abs_max, abs_max]
                        logging.info(f"Using Mag*Voice Slope limits calculated from current data: {mv_slope_range}")
                    except Exception as e:
                        # Final fallback to reasonable defaults
                        mv_slope_range = [-10, 10]
                        logging.info(f"Using default Mag*Voice Slope limits due to error: {e}")

                # Apply the limits to the plot
                fig.update_xaxes(title_text='Slope', row=1, col=col_idx, range=mv_slope_range)
            else:
                fig.add_annotation(text="Data unavailable", row=1, col=col_idx, showarrow=False)
                fig.update_xaxes(title_text='Slope', row=1, col=col_idx)
            col_idx += 1
        elif output == "Spectral Decrease":
            if spec_decrease_normalized.size > 0:
                fig.add_trace(go.Scatter(x=spec_decrease_normalized, y=time_vector, mode='lines'), row=1, col=col_idx)
                # For option 1, keep the x-axis range as [0, 1] for Spectral Decrease
                spec_decrease_range = [0, 1]
                fig.update_xaxes(title_text='Norm. Decrease', row=1, col=col_idx, range=spec_decrease_range)
                logging.info(f"Option 1: Using fixed range [0, 1] for Spectral Decrease")
            else:
                fig.add_annotation(text="Data unavailable", row=1, col=col_idx, showarrow=False)
                fig.update_xaxes(title_text='Norm. Decrease', row=1, col=col_idx)
            col_idx += 1
        elif output == "HFC":
            if hfc_normalized.size > 0:
                fig.add_trace(go.Scatter(x=hfc_normalized, y=time_vector, mode='lines'), row=1, col=col_idx)
                # For option 1, use x-axis range [0, 2] for HFC
                hfc_range = [0, 2]
                fig.update_xaxes(title_text='Norm. HFC', row=1, col=col_idx, range=hfc_range)
                logging.info(f"Option 1: Using fixed range [0, 2] for HFC")
            else:
                fig.add_annotation(text="Data unavailable", row=1, col=col_idx, showarrow=False)
                fig.update_xaxes(title_text='Norm. HFC', row=1, col=col_idx)
            col_idx += 1
        elif output == "WOSS":
            # Calculate WOSS within the function if needed, or use pre-calculated if available
            if 'WOSS' in descriptors:
                woss = descriptors['WOSS']
            elif all(k in descriptors for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                 # Recalculate WOSS using settings from plot_settings
                 woss_settings = {
                     'epsilon': plot_settings.get('epsilon', 1e-4),
                     'fdom_exponent': plot_settings.get('fdom_exponent', 2.0),
                     'hfc_p95': hfc_p95 # Use the hfc_p95 passed or calculated earlier
                 }
                 # Ensure calculate_woss is imported or defined
                 from processing import calculate_woss
                 woss = calculate_woss(descriptors, woss_settings)
            else:
                 woss = np.array([])

            if woss.size > 0:
                fig.add_trace(go.Scatter(x=woss, y=time_vector, mode='lines'), row=1, col=col_idx)
                # Try multiple possible key formats for WOSS range
                woss_range = None
                for key in ['WOSS', 'woss']:
                    if key in plot_settings:
                        woss_range = plot_settings[key]
                        logging.info(f"Found WOSS range under key '{key}': {woss_range}")
                        break
                if woss_range is None:
                    woss_range = [-3, 3]  # Default if not found

                fig.update_xaxes(title_text='WOSS', row=1, col=col_idx, range=woss_range)
            else:
                fig.add_annotation(text="Data unavailable", row=1, col=col_idx, showarrow=False)
                fig.update_xaxes(title_text='WOSS', row=1, col=col_idx)
            col_idx += 1

    # Update y-axes for all subplots
    for i in range(1, len(selected_outputs) + 1):
        # Apply time range from plot_settings['Time (Y-axis)']
        time_range = plot_settings.get('Time (Y-axis)', [0, len(time_vector) * plot_settings.get('dt', 0.004)])[::-1]
        fig.update_yaxes(title_text='Time (s)' if i == 1 else '', row=1, col=i, range=time_range)

    # Count the number of heatmap outputs that need colorbars
    heatmap_outputs = sum(1 for o in selected_outputs if o in ["Magnitude Spectrogram", "Magnitude * Voice"])

    # Set figure title and layout
    title_text = "Spectral Descriptors"
    if trace_idx is not None:
        title_text += f" for Trace {trace_idx}"
    if well_marker_name:
        title_text += f" from '{well_marker_name}'"

    fdom_exponent = plot_settings.get('fdom_exponent', 2.0)
    title_text += f"<br>(fdom^{fdom_exponent:.1f})"

    fig.update_layout(
        title={
            'text': title_text,
            'font': {'size': 15, 'family': 'Arial', 'color': 'black', 'weight': 'bold'},
            'x': 0,
            'y': 0.98,
            'xanchor': 'left',
            'yanchor': 'top',
        },
        height=850,  # Set to 850 for option 1, suboption 1
        width=max(800, 300 * len(subplot_titles) + 80 * heatmap_outputs),  # Width based on number of columns, adjusted for larger colorbars
        showlegend=False,
        margin=dict(r=70 + 80 * heatmap_outputs)  # Right margin for colorbars, increased to accommodate larger font and height
    )

    return fig

def plot_multi_trace_section(trace_data_list, time_vector, descriptors_list, plot_settings, output_type="WOSS", title=None, x_axis_values=None, x_axis_title="Trace Index"):
    """
    Plot a section view of multiple traces for a specific output type.

    Args:
        trace_data_list: List of trace data arrays
        time_vector: Time vector for the traces
        descriptors_list: List of descriptor dictionaries
        plot_settings: Dictionary containing plot settings
        output_type: Type of output to plot (default: "WOSS")
        title: Optional title for the plot
        x_axis_values: Optional array of values to use for the x-axis (e.g., inline or crossline numbers)
        x_axis_title: Optional title for the x-axis (default: "Trace Index")

    Returns:
        Plotly figure object
    """
    # Add detailed logging to help diagnose issues
    logging.info(f"plot_multi_trace_section called with output_type: {output_type}")
    logging.info(f"Number of traces: {len(trace_data_list) if trace_data_list else 'None'}")
    logging.info(f"Number of descriptors: {len(descriptors_list) if descriptors_list else 'None'}")
    logging.info(f"Time vector length: {len(time_vector) if time_vector is not None else 'None'}")
    logging.info(f"X-axis values provided: {x_axis_values is not None}")
    if x_axis_values is not None:
        logging.info(f"X-axis values length: {len(x_axis_values)}")
    logging.info(f"Plot settings keys: {list(plot_settings.keys())}")

    # Check for empty or None inputs
    if not trace_data_list:
        logging.error("trace_data_list is empty or None")
        return None

    if not descriptors_list:
        logging.error("descriptors_list is empty or None")
        return None

    if time_vector is None or len(time_vector) == 0:
        logging.error("time_vector is None or empty")
        return None

    # Check if any descriptors are None or empty
    valid_descriptors = []
    for i, desc in enumerate(descriptors_list):
        if desc is None or len(desc) == 0:
            logging.warning(f"Descriptor {i} is None or empty, replacing with empty dictionary")
            valid_descriptors.append({})
        else:
            valid_descriptors.append(desc)

    # Replace descriptors_list with valid_descriptors
    descriptors_list = valid_descriptors
    # Map output_type to internal descriptor keys
    output_key_map = {
        "Input Signal": "data",
        "Magnitude Spectrogram": "tf_map",  # Updated to match descriptor_mapping in app.py
        "Magnitude * Voice": "mag_voice",
        "Normalized dominant frequencies": "norm_fdom",
        "Normalized Dominant Frequency": "norm_fdom",  # Added for consistency
        "Spectral Slope": "spec_slope",
        "Mag*Voice Slope": "mag_voice_slope",
        "Spectral Decrease": "spec_decrease",
        "HFC": "hfc",
        "Spectral Bandwidth": "spec_bandwidth",
        "Spectral Rolloff": "spec_rolloff",
        "WOSS": "WOSS"
    }

    # Log the output_key_map for debugging
    logging.info(f"Output key map: {output_key_map}")

    # Check if the requested output_type is in the map
    if output_type not in output_key_map:
        logging.warning(f"Output type '{output_type}' not found in output_key_map. Available types: {list(output_key_map.keys())}")
        # Try to find a close match
        for key in output_key_map.keys():
            if output_type.lower() in key.lower() or key.lower() in output_type.lower():
                logging.info(f"Found potential match for '{output_type}': '{key}'")

    # Add fallback mappings for common variations
    if output_type not in output_key_map:
        # Check for common variations and add them to the map
        if "normalized dominant" in output_type.lower():
            output_key_map[output_type] = "norm_fdom"
            logging.info(f"Added fallback mapping for '{output_type}' to 'norm_fdom'")
        elif "magnitude" in output_type.lower() and "spectrogram" in output_type.lower():
            output_key_map[output_type] = "tf_map"
            logging.info(f"Added fallback mapping for '{output_type}' to 'tf_map'")
        elif "magnitude" in output_type.lower() and "voice" in output_type.lower():
            output_key_map[output_type] = "mag_voice"
            logging.info(f"Added fallback mapping for '{output_type}' to 'mag_voice'")
        elif "slope" in output_type.lower() and "voice" in output_type.lower():
            output_key_map[output_type] = "mag_voice_slope"
            logging.info(f"Added fallback mapping for '{output_type}' to 'mag_voice_slope'")
        elif "spectral slope" in output_type.lower():
            output_key_map[output_type] = "spec_slope"
            logging.info(f"Added fallback mapping for '{output_type}' to 'spec_slope'")
        elif "bandwidth" in output_type.lower():
            output_key_map[output_type] = "spec_bandwidth"
            logging.info(f"Added fallback mapping for '{output_type}' to 'spec_bandwidth'")
        elif "rolloff" in output_type.lower():
            output_key_map[output_type] = "spec_rolloff"
            logging.info(f"Added fallback mapping for '{output_type}' to 'spec_rolloff'")
        elif "decrease" in output_type.lower():
            output_key_map[output_type] = "spec_decrease"
            logging.info(f"Added fallback mapping for '{output_type}' to 'spec_decrease'")
        elif "hfc" in output_type.lower():
            output_key_map[output_type] = "hfc"
            logging.info(f"Added fallback mapping for '{output_type}' to 'hfc'")
        elif "woss" in output_type.lower():
            output_key_map[output_type] = "WOSS"
            logging.info(f"Added fallback mapping for '{output_type}' to 'WOSS'")
        elif "input" in output_type.lower() or "signal" in output_type.lower():
            output_key_map[output_type] = "data"
            logging.info(f"Added fallback mapping for '{output_type}' to 'data'")

    # Create data matrix for the selected output
    data_matrix = None
    logging.info(f"Creating data matrix for output type: {output_type}")

    if output_type == "Input Signal":
        # For input signal, use the trace data directly without transposing
        logging.info(f"Input Signal: Using trace data directly, shape: {np.array([t for t in trace_data_list]).shape if trace_data_list else 'None'}")
        data_matrix = np.array([t for t in trace_data_list])  # Removed transpose operation
    elif output_type == "Magnitude Spectrogram" or output_type == "Magnitude * Voice":
        # For spectrograms, we need to handle them differently
        # Since spectrograms are 2D (frequency x time), we can't easily display them in a section view
        # Instead, we'll create a section using the average magnitude across frequencies for each time sample

        key = output_key_map.get(output_type)
        logging.info(f"Spectrogram: Using key '{key}' for {output_type}")

        if key:
            data_list = []
            for i, descriptor in enumerate(descriptors_list):
                # Log available keys in descriptor
                logging.info(f"Descriptor {i} keys: {list(descriptor.keys()) if descriptor else 'None'}")

                # Special handling for Magnitude Spectrogram which could be in 'tf_map' or 'mag'
                if output_type == "Magnitude Spectrogram" and "mag" in descriptor and isinstance(descriptor["mag"], np.ndarray):
                    # Use 'mag' if available for backward compatibility
                    spec_data = descriptor["mag"]
                    logging.info(f"Using 'mag' for Magnitude Spectrogram, shape: {spec_data.shape}")
                elif key in descriptor and isinstance(descriptor[key], np.ndarray):
                    spec_data = descriptor[key]
                    logging.info(f"Using '{key}' for {output_type}, shape: {spec_data.shape}")
                else:
                    # Create zeros array of the same length as time_vector
                    logging.info(f"Key '{key}' not found or not an array in descriptor {i}, using zeros")
                    data_list.append(np.zeros_like(time_vector))
                    continue

                # Take the mean across frequency dimension for each time sample
                if spec_data.ndim > 1:
                    # For 2D spectrograms (freq x time), take mean across frequency
                    mean_data = np.mean(spec_data, axis=0)
                    logging.info(f"Taking mean across frequency dimension, result shape: {mean_data.shape}")
                    data_list.append(mean_data)
                else:
                    # If already 1D, use as is
                    logging.info(f"Using 1D data as is, shape: {spec_data.shape}")
                    data_list.append(spec_data)

            if data_list:
                data_matrix = np.array(data_list)  # Removed transpose operation
                logging.info(f"Created data matrix for {output_type}, shape: {data_matrix.shape}")
            else:
                logging.warning(f"No data collected for {output_type}")
    elif output_type == "WOSS":
        # Calculate WOSS if needed
        logging.info(f"WOSS: Checking for WOSS data or calculating it")
        woss_list = []
        for i, descriptor in enumerate(descriptors_list):
            logging.info(f"Descriptor {i} keys: {list(descriptor.keys()) if descriptor else 'None'}")
            if 'WOSS' in descriptor:
                woss_list.append(descriptor['WOSS'])
                logging.info(f"Using existing WOSS data, shape: {descriptor['WOSS'].shape}")
            elif all(k in descriptor for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                try:
                    woss = calculate_woss(descriptor, plot_settings)
                    woss_list.append(woss)
                    logging.info(f"Calculated WOSS data, shape: {woss.shape}")
                except Exception as e:
                    logging.error(f"Error calculating WOSS: {e}")
                    woss_list.append(np.zeros_like(time_vector))
            else:
                # Create zeros array of the same length as time_vector
                logging.info(f"Missing required keys for WOSS calculation in descriptor {i}, using zeros")
                woss_list.append(np.zeros_like(time_vector))

        if woss_list:
            data_matrix = np.array(woss_list)  # Removed transpose operation
            logging.info(f"Created data matrix for WOSS, shape: {data_matrix.shape}")
        else:
            logging.warning(f"No WOSS data collected")
    else:
        # For other outputs, extract from descriptors
        key = output_key_map.get(output_type)
        logging.info(f"Other output type: Using key '{key}' for {output_type}")

        if key:
            data_list = []
            for i, descriptor in enumerate(descriptors_list):
                logging.info(f"Descriptor {i} keys: {list(descriptor.keys()) if descriptor else 'None'}")
                if key in descriptor:
                    data_list.append(descriptor[key])
                    logging.info(f"Found key '{key}' in descriptor {i}, shape: {descriptor[key].shape}")
                else:
                    # Create zeros array of the same length as time_vector
                    logging.info(f"Key '{key}' not found in descriptor {i}, using zeros")
                    data_list.append(np.zeros_like(time_vector))

            if data_list:
                data_matrix = np.array(data_list)  # Removed transpose operation
                logging.info(f"Created data matrix for {output_type}, shape: {data_matrix.shape}")
            else:
                logging.warning(f"No data collected for {output_type}")
        else:
            logging.warning(f"No mapping found for output type: {output_type}")

    if data_matrix is None:
        logging.warning(f"Could not create data matrix for {output_type}")
        return None

    # Create figure
    try:
        fig = go.Figure()
        logging.info(f"Creating figure for {output_type}")

        # Get colormap and range for the selected output
        colormap_key = f'colormap_{output_type.lower().replace(" ", "_").replace("*", "")}'
        colormap = plot_settings.get(colormap_key, 'RdBu')
        logging.info(f"Using colormap key: {colormap_key}, value: {colormap}")

        if output_type == "Input Signal":
            colormap = plot_settings.get('section_colormap', 'RdBu')
            logging.info(f"Input Signal: Using section_colormap: {colormap}")

        # Get value range for the selected output
        value_range = plot_settings.get(output_type, [-1, 1])
        logging.info(f"Initial value range from plot_settings[{output_type}]: {value_range}")

        if output_type == "Spectral Decrease":
            # First check if specific cmap min/max keys exist
            if 'spectral_decrease_cmap_min' in plot_settings and 'spectral_decrease_cmap_max' in plot_settings:
                value_range = [plot_settings['spectral_decrease_cmap_min'], plot_settings['spectral_decrease_cmap_max']]
                logging.info(f"Spectral Decrease: Using range from cmap min/max: {value_range}")
            else:
                # Fallback to the range passed as a list under 'Spectral Decrease' key
                value_range = plot_settings.get('Spectral Decrease', [0, 1])
                logging.info(f"Spectral Decrease: Using range from 'Spectral Decrease' key: {value_range}")
        elif output_type == "HFC":
            # First check if specific cmap min/max keys exist
            if 'hfc_cmap_min' in plot_settings and 'hfc_cmap_max' in plot_settings:
                value_range = [plot_settings['hfc_cmap_min'], plot_settings['hfc_cmap_max']]
                logging.info(f"HFC: Using range from cmap min/max: {value_range}")
            else:
                # Fallback to the range passed as a list under 'HFC' key
                value_range = plot_settings.get('HFC', [0, 2])  # Default is [0, 2] to match option 1
                logging.info(f"HFC: Using range from 'HFC' key: {value_range}")
        elif output_type == "Spectral Bandwidth" or output_type == "Spectral Rolloff":
            value_range = [0, 50]
            logging.info(f"{output_type}: Using fixed range: {value_range}")
        elif output_type == "WOSS":
            value_range = plot_settings.get('WOSS', [-3, 3])
            logging.info(f"WOSS: Using range: {value_range}")
        elif output_type == "Magnitude Spectrogram":
            value_range = plot_settings.get('Magnitude Spectrogram', [0, 1])
            logging.info(f"Magnitude Spectrogram: Using range: {value_range}")
        elif output_type == "Magnitude * Voice":
            value_range = plot_settings.get('Magnitude * Voice', [-1, 1])
            logging.info(f"Magnitude * Voice: Using range: {value_range}")
        elif output_type == "Spectral Slope":
            value_range = plot_settings.get('Spectral Slope', [-1, 1])
            logging.info(f"Spectral Slope: Using range: {value_range}")
        elif output_type == "Mag*Voice Slope":
            value_range = plot_settings.get('Mag*Voice Slope', [-1, 1])
            logging.info(f"Mag*Voice Slope: Using range: {value_range}")

        logging.info(f"Final value range: {value_range}")

        # Prepare x-axis values
        x_values = x_axis_values if x_axis_values is not None else np.arange(data_matrix.shape[0])
        logging.info(f"X-axis values: {x_values[:5]}{'...' if len(x_values) > 5 else ''}, length: {len(x_values)}")
        logging.info(f"Data matrix shape: {data_matrix.shape}")
        logging.info(f"Time vector length: {len(time_vector)}")

        # Add heatmap - transpose data_matrix for visualization since we're now using [traces, time_samples] format
        fig.add_trace(go.Heatmap(
            z=data_matrix.T,  # Transpose for visualization
            x=x_values,
            y=time_vector,
            colorscale=colormap,
            zmin=value_range[0],
            zmax=value_range[1]
        ))
        logging.info(f"Added heatmap trace to figure")

        # Set layout
        if title is None:
            title = f"{output_type} Section"

        fig.update_layout(
            title=title,
            xaxis_title=x_axis_title,
            yaxis_title="Time (s)",
            yaxis=dict(autorange="reversed"),  # Reverse y-axis to match seismic convention
            height=700,
            width=900
        )
        logging.info(f"Updated figure layout with title: {title}")

        return fig

    except Exception as e:
        logging.error(f"Error creating multi-trace section plot: {e}")
        return None

def create_analysis_plots(results, analysis_mode, plot_settings):
    """Create visualization plots for analysis results."""
    try:
        # This is a placeholder implementation for the analysis plots
        # In a real implementation, you would create comprehensive visualizations
        # based on the analysis mode and results

        logging.info(f"Creating analysis plots for mode: {analysis_mode}")

        # Create a simple placeholder figure
        fig = go.Figure()

        # Add a simple scatter plot as placeholder
        if results.get('descriptors') and len(results['descriptors']) > 0:
            # Extract some data for visualization
            trace_count = len(results['descriptors'])
            x_values = list(range(trace_count))

            # Try to get WOSS values if available
            y_values = []
            for desc in results['descriptors']:
                if isinstance(desc, dict) and 'WOSS' in desc:
                    woss_val = desc['WOSS']
                    if isinstance(woss_val, np.ndarray):
                        y_values.append(float(np.mean(woss_val)))
                    else:
                        y_values.append(float(woss_val))
                else:
                    y_values.append(0.0)

            fig.add_trace(go.Scatter(
                x=x_values,
                y=y_values,
                mode='lines+markers',
                name='WOSS Values',
                line=dict(color='blue', width=2),
                marker=dict(size=4)
            ))

            fig.update_layout(
                title=f"Analysis Results - {analysis_mode}",
                xaxis_title="Trace Index",
                yaxis_title="WOSS Value",
                width=800,
                height=400,
                showlegend=True
            )
        else:
            # No data available
            fig.add_annotation(
                text="No visualization data available",
                x=0.5, y=0.5,
                xref="paper", yref="paper",
                showarrow=False,
                font=dict(size=16)
            )

            fig.update_layout(
                title=f"Analysis Results - {analysis_mode}",
                width=800,
                height=400
            )

        return fig

    except Exception as e:
        logging.error(f"Failed to create analysis plots: {e}")

        # Return a simple error figure
        fig = go.Figure()
        fig.add_annotation(
            text=f"Visualization error: {str(e)}",
            x=0.5, y=0.5,
            xref="paper", yref="paper",
            showarrow=False,
            font=dict(size=14, color="red")
        )

        fig.update_layout(
            title="Visualization Error",
            width=800,
            height=400
        )

        return fig
    except Exception as e:
        logging.error(f"Error creating figure: {e}", exc_info=True)
        return None