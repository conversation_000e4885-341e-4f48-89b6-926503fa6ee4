
import math
import numpy as np
from tqdm import tqdm
import sys
import os
import segyio
import logging

def parse_polyline_string(coord_string):
    """
    Parse a string of polyline coordinates into a list of vertex tuples.

    This function converts a string of coordinate pairs into a list of (x,y) tuples.
    It supports multiple formats:
    - Semicolon-separated pairs with comma-separated coordinates: 'x1,y1; x2,y2; ...'
    - Line-by-line format with space, tab, or comma-separated coordinates

    Args:
        coord_string: String containing coordinate pairs

    Returns:
        list: List of (x,y) tuples representing polyline vertices

    Raises:
        ValueError: If the string format is invalid or contains fewer than 2 vertices
    """
    vertices = []

    # Split by lines first
    lines = coord_string.strip().split('\n')

    # If only one line, try semicolon separation
    if len(lines) == 1 and ';' in coord_string:
        pairs = coord_string.strip().split(';')

        # Handle empty string case
        if not pairs or not pairs[0]:
            raise ValueError("Coordinate string is empty.")

        # Process each coordinate pair
        for pair in pairs:
            try:
                # Try comma separation first
                if ',' in pair:
                    coords = pair.strip().split(',')
                else:
                    # Fall back to space separation
                    coords = pair.strip().split()

                if len(coords) < 2:
                    raise ValueError(f"Invalid coordinate pair format: '{pair}'. Expected 'x,y' or 'x y'.")

                # Convert to floating point values (use first two values if more are present)
                x = float(coords[0].strip())
                y = float(coords[1].strip())
                vertices.append((x, y))
            except ValueError as e:
                raise ValueError(f"Could not parse coordinate pair '{pair}': {e}")
    else:
        # Process line by line
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):  # Skip empty lines and comments
                continue

            try:
                # Try different delimiters
                if ',' in line:
                    coords = line.split(',')
                elif '\t' in line:
                    coords = line.split('\t')
                else:
                    coords = line.split()

                if len(coords) < 2:
                    continue  # Skip lines without enough coordinates

                # Convert to floating point values
                x = float(coords[0].strip())
                y = float(coords[1].strip())
                vertices.append((x, y))
            except ValueError as e:
                raise ValueError(f"Could not parse coordinate pair '{line}': {e}")

    # Ensure we have at least 2 vertices to define a polyline
    if len(vertices) < 2:
        raise ValueError("Polyline must have at least 2 vertices.")

    return vertices

def distance_point_to_segment(px, py, x1, y1, x2, y2):
    """
    Calculate the shortest distance from a point to a line segment.

    This function computes the minimum distance from point (px,py) to the line
    segment defined by endpoints (x1,y1) and (x2,y2). It handles special cases
    like zero-length segments and points that project outside the segment.

    Args:
        px, py: Coordinates of the point
        x1, y1: Coordinates of the first endpoint of the line segment
        x2, y2: Coordinates of the second endpoint of the line segment

    Returns:
        float: The minimum distance from the point to the line segment
    """
    # Calculate squared length of the segment
    seg_len_sq = (x2 - x1)**2 + (y2 - y1)**2

    # Handle degenerate case where segment is essentially a point
    if seg_len_sq < 1e-12:
        return math.sqrt((px - x1)**2 + (py - y1)**2)

    # Project point onto the line containing the segment
    # Parameter t represents position along the infinite line (0=start, 1=end)
    t = ((px - x1) * (x2 - x1) + (py - y1) * (y2 - y1)) / seg_len_sq

    # Clamp t to the segment [0, 1] to handle points that project outside the segment
    t = max(0, min(1, t))

    # Find the closest point on the segment
    closest_x = x1 + t * (x2 - x1)
    closest_y = y1 + t * (y2 - y1)

    # Calculate Euclidean distance from the original point to the closest point on the segment
    dist = math.sqrt((px - closest_x)**2 + (py - closest_y)**2)
    return dist

def find_traces_near_polyline(header_loader, polyline_vertices, max_distance):
    """
    Find trace indices within a specified distance of a polyline.

    This function calculates the minimum distance from each trace location to
    a polyline defined by a series of vertices. Traces within the specified
    maximum distance are selected. It uses the distance_point_to_segment helper
    function to efficiently calculate distances to line segments.

    Args:
        header_loader: SegyHeaderLoader object containing trace coordinates
        polyline_vertices: List of (x,y) tuples defining the polyline vertices
        max_distance: Maximum distance threshold for trace selection

    Returns:
        list: Indices of traces within the specified distance of the polyline
    """
    selected_indices = []
    trace_coords = np.column_stack((header_loader.x_coords, header_loader.y_coords))

    print(f"\nFinding traces within {max_distance} units of the defined polyline...")
    # Use tqdm for progress bar to show processing status
    for i in tqdm(range(len(trace_coords)), desc="Checking trace proximity"):
        trace_x, trace_y = trace_coords[i]
        min_dist_to_polyline = float('inf')

        # Iterate through each segment of the polyline
        for j in range(len(polyline_vertices) - 1):
            x1, y1 = polyline_vertices[j]      # Start point of segment
            x2, y2 = polyline_vertices[j+1]    # End point of segment

            # Calculate distance from trace to this segment
            dist = distance_point_to_segment(trace_x, trace_y, x1, y1, x2, y2)

            # Keep track of minimum distance to any segment
            min_dist_to_polyline = min(min_dist_to_polyline, dist)

        # Check if the minimum distance is within the tolerance
        if min_dist_to_polyline <= max_distance:
            selected_indices.append(header_loader.unique_indices[i])

    print(f"Found {len(selected_indices)} traces near the polyline.")
    return selected_indices

def get_suggested_batch_size():
    """
    Determine optimal batch size based on available GPU memory.

    This function attempts to query the available GPU memory using CuPy
    and calculates an appropriate batch size for processing. If GPU memory
    information is not available, it falls back to conservative defaults.

    Returns:
        tuple: (suggested_batch_size, free_memory_mb)
            - suggested_batch_size: Recommended batch size for processing
            - free_memory_mb: Available GPU memory in megabytes
    """
    try:
        # Try to import CuPy and get GPU memory information
        import cupy as cp
        free_bytes, _ = cp.cuda.runtime.memGetInfo()
        free_mb = free_bytes / (1024 ** 2)

        # Calculate batch size based on available memory
        # Scale with available memory but keep within reasonable bounds
        suggested_batch = min(max(10, int(free_mb / 100)), 50)  # Adjust based on available GPU memory
    except Exception as e:
        # Fall back to conservative defaults if GPU memory info is unavailable
        free_mb = 4096  # fallback guess
        suggested_batch = 20  # Conservative default
        print(f"Could not determine optimal batch size from GPU memory: {e}. Using default batch size of {suggested_batch}.")

    print(f"Suggesting batch size: {suggested_batch} (free GPU RAM: {free_mb:.1f} MB)")
    return suggested_batch, free_mb

def custom_excepthook(exc_type, exc_value, exc_traceback):
    """Custom exception handler that sanitizes error messages for GUI display.

    This function replaces backticks with single quotes in error messages to prevent
    formatting issues in GUI message boxes and ensures all exceptions are properly logged.

    Args:
        exc_type: Exception type
        exc_value: Exception value/message
        exc_traceback: Exception traceback object
    """
    try:
        # Replace any backticks in error messages with quotes
        if isinstance(exc_value, str):
            exc_value = exc_value.replace("`", "'")
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
    except Exception as e:
        print(f"Error in custom exception handler: {e}")
        sys.__excepthook__(exc_type, exc_value, exc_traceback)


def save_to_numpy(file_path, numpy_data):
    """
    Save NumPy data to a .npy file.

    Args:
        file_path (str): Path where the NumPy file will be saved. If no extension is provided,
                         '.npy' will be appended.
        numpy_data (numpy.ndarray or object): NumPy array or object with get_cube() method
                                             containing the data to be saved.

    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        # Ensure the file has .npy extension
        if not file_path.endswith('.npy'):
            file_path += '.npy'

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)

        # Extract data from numpy_data if it has get_cube method
        if hasattr(numpy_data, 'get_cube'):
            data_to_save = numpy_data.get_cube()
        else:
            data_to_save = numpy_data

        # Save the NumPy array
        np.save(file_path, data_to_save)
        logging.info(f"Successfully saved NumPy data to {file_path}")
        return True

    except Exception as e:
        logging.error(f"Error saving NumPy data to {file_path}: {e}")
        return False


def save_to_segy_2d(seismic, output_path, numpy_data, session_state):
    """
    Save 2D seismic data to a SEG-Y file.

    Args:
        seismic (SeismicData): Original seismic data object containing header information
        output_path (str): Path where the SEG-Y file will be saved
        numpy_data (numpy.ndarray or object): NumPy array or object with data to be saved
        session_state: Streamlit session state for progress tracking

    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        # Ensure the file has .sgy extension
        if not output_path.endswith(('.sgy', '.segy')):
            output_path += '.sgy'

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

        # Get the original file name
        original_file = seismic.get_file_name()

        # Extract data from numpy_data if it has appropriate methods
        if hasattr(numpy_data, 'get_iline'):
            data = numpy_data.get_iline()
        else:
            data = numpy_data

        # Ensure data is in the right shape for 2D
        if len(data.shape) > 2:
            data = data.reshape(data.shape[0], data.shape[1])

        # Open the original file to copy headers
        with segyio.open(original_file, strict=False) as src:
            # Create a specification for the output file
            spec = segyio.tools.metadata(src)

            # Update spec with the new dimensions if needed
            if spec.samples.size != data.shape[1]:
                spec.samples = np.arange(data.shape[1])

            # Create the output file
            with segyio.create(output_path, spec) as dst:
                # Copy the textual header
                dst.text[0] = src.text[0]

                # Write the data traces
                for i in range(min(data.shape[0], len(dst.trace))):
                    dst.trace[i] = data[i, :]
                    dst.header[i] = src.header[i]  # Copy trace headers

        logging.info(f"Successfully saved 2D SEG-Y data to {output_path}")
        return True

    except Exception as e:
        logging.error(f"Error saving 2D SEG-Y data to {output_path}: {e}")
        return False


def save_to_segy_3d(seismic, output_path, numpy_data, session_state):
    """
    Save 3D seismic data to a SEG-Y file.

    Args:
        seismic (SeismicData): Original seismic data object containing header information
        output_path (str): Path where the SEG-Y file will be saved
        numpy_data (numpy.ndarray or object): NumPy array or object with data to be saved
        session_state: Streamlit session state for progress tracking

    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        # Ensure the file has .sgy extension
        if not output_path.endswith(('.sgy', '.segy')):
            output_path += '.sgy'

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

        # Get the original file name
        original_file = seismic.get_file_name()

        # Extract data from numpy_data if it has get_cube method
        if hasattr(numpy_data, 'get_cube'):
            data = numpy_data.get_cube()
        else:
            data = numpy_data

        # Open the original file to copy headers
        with segyio.open(original_file, iline=int(seismic.get_iline_byte()), xline=int(seismic.get_xline_byte())) as src:
            # Create a specification for the output file
            spec = segyio.tools.metadata(src)

            # Update spec with the new dimensions if needed
            if data.shape[2] != spec.samples.size:
                spec.samples = np.arange(data.shape[2])

            # Create the output file
            with segyio.create(output_path, spec) as dst:
                # Copy the textual header
                dst.text[0] = src.text[0]

                # Get the inline and crossline numbers
                ilines = src.ilines
                xlines = src.xlines

                # Create a progress bar for Streamlit if available
                if hasattr(session_state, 'get') and callable(getattr(session_state, 'get')):
                    import streamlit as st
                    progress_bar = st.progress(0)
                    total_traces = len(ilines) * len(xlines)
                    trace_count = 0

                # Write the data traces
                for il_idx, il in enumerate(ilines):
                    if il_idx < data.shape[0]:  # Check if we have this inline in our data
                        for xl_idx, xl in enumerate(xlines):
                            if xl_idx < data.shape[1]:  # Check if we have this crossline in our data
                                # Calculate the trace index
                                trace_idx = il_idx * len(xlines) + xl_idx

                                # Write the trace data
                                if trace_idx < len(dst.trace):
                                    dst.trace[trace_idx] = data[il_idx, xl_idx, :]
                                    dst.header[trace_idx] = src.header[trace_idx]  # Copy trace headers

                                    # Update inline and crossline in the header
                                    dst.header[trace_idx][int(seismic.get_iline_byte())] = il
                                    dst.header[trace_idx][int(seismic.get_xline_byte())] = xl

                                # Update progress if Streamlit is available
                                if 'progress_bar' in locals():
                                    trace_count += 1
                                    progress_bar.progress(trace_count / total_traces)

                # Clean up progress bar if it exists
                if 'progress_bar' in locals():
                    progress_bar.empty()

        logging.info(f"Successfully saved 3D SEG-Y data to {output_path}")
        return True

    except Exception as e:
        logging.error(f"Error saving 3D SEG-Y data to {output_path}: {e}")
        return False
