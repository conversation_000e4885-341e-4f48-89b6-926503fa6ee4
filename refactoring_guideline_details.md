# WOSS Tool: Granular Refactoring Guide (From Scratch)

This document provides a complete, code-level guide to refactor the WOSS Seismic Analysis Tool from a single script (`app_ref.py`) into a modular, multi-page Streamlit application. Follow these steps precisely to build the new structure.

## ✅ CPU Implementation Status

**GOOD NEWS: CPU Implementation Now Available!**

The codebase now includes:
- GPU implementation in `dlogst_spec_descriptor_gpu.py`
- CPU fallback implementation in `dlogst_spec_descriptor_cpu.py`
- Updated `app_ref.py` with proper CPU fallback logic

The application will now work on both GPU and CPU-only systems, with automatic fallback to CPU when GPU is not available.

---

## Phase 0: URGENT - Create CPU Implementation

**⚠️ DO THIS FIRST BEFORE ANY OTHER REFACTORING ⚠️**

The current application will completely fail on CPU-only systems. You must create the CPU implementation immediately.

### Step 0.1: Create CPU Implementation File

**Action:** Create a new file `dlogst_spec_descriptor_cpu.py` in the root directory (alongside the existing files) and add the complete CPU implementation:

```python
# dlogst_spec_descriptor_cpu.py
import numpy as np
from scipy.signal import find_peaks
from scipy.fft import fft, fftfreq
import logging

def dlogst_spec_descriptor_cpu(trace, dt, fmax=None, **kwargs):
    """
    CPU implementation of spectral descriptor calculations.
    
    Args:
        trace: 1D numpy array of seismic trace
        dt: Sampling interval in seconds
        fmax: Maximum frequency for analysis (Hz)
        **kwargs: Additional parameters for spectral analysis
    
    Returns:
        dict: Dictionary containing calculated spectral descriptors
    """
    trace = np.asarray(trace, dtype=np.float32)
    
    # Handle edge cases
    if len(trace) == 0:
        return {}
        
    # Parameters with defaults
    use_band_limited = kwargs.get('use_band_limited', False)
    b1 = kwargs.get('b1', 5.0)
    b2 = kwargs.get('b2', 40.0)
    shape = kwargs.get('shape', 1.0)
    kmax = kwargs.get('kmax', 120.0)
    int_val = kwargs.get('int_val', 35.0)
    p_bandwidth = kwargs.get('p_bandwidth', 2.0)
    roll_percent = kwargs.get('roll_percent', 0.85)
    
    # Calculate spectrum
    spectrum = fft(trace)
    freqs = fftfreq(len(trace), dt)
    
    # Keep only positive frequencies
    n_positive = len(freqs) // 2
    freqs = freqs[:n_positive]
    spectrum = spectrum[:n_positive]
    
    if fmax is not None:
        freq_mask = freqs <= fmax
        freqs = freqs[freq_mask]
        spectrum = spectrum[freq_mask]
    
    # Magnitude spectrum
    magnitude = np.abs(spectrum)
    
    # Avoid division by zero
    magnitude = np.where(magnitude < 1e-10, 1e-10, magnitude)
    
    # Calculate voice (instantaneous frequency)
    voice = np.zeros_like(magnitude)
    if len(magnitude) > 1:
        phase = np.angle(spectrum)
        voice[1:] = np.diff(phase) / (2 * np.pi * dt)
    
    # Logistic transform for voice
    voice_transformed = kmax / (1 + np.exp(-shape * (voice - int_val)))
    
    # Magnitude * Voice
    mag_voice = magnitude * voice_transformed
    
    # Spectral descriptors
    descriptors = {
        'data': trace,
        'magnitude': magnitude,
        'voice': voice_transformed,
        'mag_voice': mag_voice
    }
    
    # Calculate slopes
    if use_band_limited and len(freqs) > 1:
        freq_mask = (freqs >= b1) & (freqs <= b2)
        if np.any(freq_mask):
            f_band = freqs[freq_mask]
            mag_band = magnitude[freq_mask]
            voice_band = voice_transformed[freq_mask]
            
            # Spectral slope
            if len(f_band) > 1:
                descriptors['spec_slope'] = calculate_slope_cpu(f_band, mag_band)
            
            # Mag*Voice slope
            if len(f_band) > 1:
                descriptors['mag_voice_slope'] = calculate_slope_cpu(f_band, mag_band * voice_band)
    else:
        # Full band
        if len(freqs) > 1:
            descriptors['spec_slope'] = calculate_slope_cpu(freqs, magnitude)
            descriptors['mag_voice_slope'] = calculate_slope_cpu(freqs, mag_voice)
    
    # Spectral decrease
    descriptors['spec_decrease'] = calculate_decrease_cpu(magnitude)
    
    # High Frequency Content (HFC)
    descriptors['hfc'] = calculate_hfc_cpu(freqs, magnitude)
    
    # Spectral bandwidth
    descriptors['spec_bandwidth'] = calculate_bandwidth_cpu(freqs, magnitude, p_bandwidth)
    
    # Spectral rolloff
    descriptors['spec_rolloff'] = calculate_rolloff_cpu(freqs, magnitude, roll_percent)
    
    # Normalized dominant frequency
    descriptors['norm_fdom'] = calculate_norm_fdom_cpu(freqs, magnitude)
    
    return descriptors

def calculate_slope_cpu(freqs, signal):
    """Calculate spectral slope using least squares fit."""
    if len(freqs) < 2:
        return np.float32(0.0)
    
    # Use log scale to avoid numerical issues
    log_freqs = np.log(freqs + 1e-10)
    log_signal = np.log(signal + 1e-10)
    
    # Least squares fit
    n = len(log_freqs)
    sum_x = np.sum(log_freqs)
    sum_y = np.sum(log_signal)
    sum_xy = np.sum(log_freqs * log_signal)
    sum_x2 = np.sum(log_freqs * log_freqs)
    
    denominator = n * sum_x2 - sum_x * sum_x
    if abs(denominator) < 1e-10:
        return np.float32(0.0)
    
    slope = (n * sum_xy - sum_x * sum_y) / denominator
    return np.float32(slope)

def calculate_decrease_cpu(signal):
    """Calculate spectral decrease."""
    if len(signal) <= 1:
        return np.float32(0.0)
    
    k = np.arange(1, len(signal))
    numerator = np.sum((signal[1:] - signal[0]) / k)
    denominator = np.sum(signal)
    
    if denominator < 1e-10:
        return np.float32(0.0)
    
    return np.float32(numerator / denominator)

def calculate_hfc_cpu(freqs, magnitude):
    """Calculate High Frequency Content."""
    if len(freqs) == 0:
        return np.float32(0.0)
    
    # Weight by frequency
    hfc = np.sum(freqs * magnitude)
    return np.float32(hfc)

def calculate_bandwidth_cpu(freqs, magnitude, p):
    """Calculate spectral bandwidth."""
    if len(freqs) == 0:
        return np.float32(0.0)
    
    # Centroid
    total_power = np.sum(magnitude)
    if total_power < 1e-10:
        return np.float32(0.0)
    
    centroid = np.sum(freqs * magnitude) / total_power
    
    # Bandwidth
    bandwidth = np.sum(((freqs - centroid) ** p) * magnitude) / total_power
    return np.float32(bandwidth ** (1/p))

def calculate_rolloff_cpu(freqs, magnitude, roll_percent):
    """Calculate spectral rolloff."""
    if len(freqs) == 0:
        return np.float32(0.0)
    
    cumsum = np.cumsum(magnitude)
    total = cumsum[-1]
    
    if total < 1e-10:
        return np.float32(0.0)
    
    threshold = total * roll_percent
    rolloff_idx = np.where(cumsum >= threshold)[0]
    
    if len(rolloff_idx) == 0:
        return freqs[-1]
    
    return np.float32(freqs[rolloff_idx[0]])

def calculate_norm_fdom_cpu(freqs, magnitude):
    """Calculate normalized dominant frequency."""
    if len(freqs) == 0:
        return np.float32(0.0)
    
    # Find peaks
    peaks, _ = find_peaks(magnitude, height=np.max(magnitude) * 0.1)
    
    if len(peaks) == 0:
        # Use maximum as dominant frequency
        max_idx = np.argmax(magnitude)
        fdom = freqs[max_idx]
    else:
        # Use highest peak
        peak_heights = magnitude[peaks]
        max_peak_idx = peaks[np.argmax(peak_heights)]
        fdom = freqs[max_peak_idx]
    
    # Normalize by Nyquist frequency
    nyquist = freqs[-1] if len(freqs) > 0 else 1.0
    return np.float32(fdom / nyquist)
```

### Step 0.2: Update app_ref.py to Use CPU Implementation

**Action:** Replace the NotImplementedError functions in `app_ref.py` with the CPU implementation:

Find these lines in `app_ref.py`:
```python
def dlogst_spec_descriptor_gpu(*args, **kwargs):
    st.error("GPU function dlogst_spec_descriptor_gpu not available.")
    raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu not available.")
```

Replace with:
```python
def dlogst_spec_descriptor_gpu(*args, **kwargs):
    st.warning("GPU not available, using CPU implementation.")
    from dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu
    return dlogst_spec_descriptor_cpu(*args, **kwargs)
```

### Step 0.3: Test CPU Implementation

**Action:** Test the CPU implementation before proceeding:

1. Run the existing Streamlit app
2. Verify it works on a CPU-only system
3. Check that all spectral descriptors are calculated correctly

Only after confirming the CPU implementation works, proceed with the full refactoring.

---

## Phase 1: Project Setup

### Step 1.1: Create Directories

In your project's root folder, create the following directories. This structure separates concerns and makes the application maintainable.

```bash
mkdir common
mkdir pages
mkdir utils
```

### Step 1.2: Create `requirements.txt`

This file will list all Python packages required to run the application.

**Action:** Create a new file named `requirements.txt` and add the following content:

```
streamlit>=1.28.0
numpy>=1.21.0
pandas>=1.3.0
segyio>=1.9.0
torch>=1.9.0
plotly>=5.0.0
tqdm>=4.62.0
scipy>=1.7.0
logging-config>=1.0.2
# Add cupy-cudaXXX matching your system's CUDA version, e.g., cupy-cuda11x
# cupy-cuda11x>=9.0.0  # Uncomment and adjust version as needed
```

---

## Phase 2: Create the `common` Module

This module contains Python scripts with code shared across the entire application, such as constants, session state management, and reusable UI elements.

### Step 2.1: `common/constants.py`

This file centralizes all static application-wide constants to avoid "magic numbers" and strings in the code.

**Action:** Create a new file `common/constants.py` and add the following code:

```python
# common/constants.py
APP_TITLE = "WOSS Seismic Analysis Tool"

# Outputs for single trace analysis
AVAILABLE_OUTPUTS_SINGLE = [
    "Input Signal", "Magnitude Spectrogram", "Magnitude * Voice",
    "Normalized dominant frequencies", "Spectral Slope", "Spectral Bandwidth",
    "Spectral Rolloff", "Mag*Voice Slope", "Spectral Decrease", "HFC", "WOSS"
]

# Outputs for multi-trace/section analysis
AVAILABLE_OUTPUTS_MULTI = [
    "Input Signal", "Magnitude Spectrogram", "Magnitude * Voice",
    "Normalized dominant frequencies", "Spectral Slope", "Spectral Bandwidth",
    "Spectral Rolloff", "Mag*Voice Slope", "Spectral Decrease", "HFC", "WOSS"
]

# Outputs for section/polyline modes (excluding spectral images)
AVAILABLE_OUTPUTS_SECTION = [
    "Input Signal", "Normalized dominant frequencies", "Spectral Slope", "Spectral Bandwidth",
    "Spectral Rolloff", "Mag*Voice Slope", "Spectral Decrease", "HFC", "WOSS"
]

# Internal names used in descriptor dicts and for export keys
EXPORTABLE_ATTR_INTERNAL_NAMES = [
    "data", "mag_voice_slope", "spec_decrease", "hfc",
    "spec_bandwidth", "spec_rolloff", "WOSS", "norm_fdom"
]

# Display names corresponding to EXPORTABLE_ATTR_INTERNAL_NAMES
EXPORTABLE_ATTR_DISPLAY_NAMES = [
    "Original Seismic Amplitude", "Magnitude*Voice Slope", "Spectral Decrease",
    "High Frequency Content (HFC)", "Spectral Bandwidth", "Spectral Rolloff",
    "WOSS (Weighted-Optimum Spectral Shape)", "Normalized Dominant Frequency"
]

# Mapping for easy lookup
ATTR_NAME_MAP = dict(zip(EXPORTABLE_ATTR_DISPLAY_NAMES, EXPORTABLE_ATTR_INTERNAL_NAMES))
REVERSE_ATTR_NAME_MAP = dict(zip(EXPORTABLE_ATTR_INTERNAL_NAMES, EXPORTABLE_ATTR_DISPLAY_NAMES))

# Default min/max values for descriptor plots
DESCRIPTOR_LIMITS = {
    "Input Signal": {"min": None, "max": None},
    "HFC": {"min": None, "max": None},
    "Spectral Decrease": {"min": None, "max": None},
    "Spectral Slope": {"min": None, "max": None},
    "Mag*Voice Slope": {"min": None, "max": None},
    "Spectral Bandwidth": {"min": None, "max": None},
    "Spectral Rolloff": {"min": None, "max": None},
    "Magnitude Spectrogram": {"min": None, "max": None},
    "Magnitude * Voice": {"min": None, "max": None},
    "Normalized Dominant Frequency": {"min": None, "max": None},
    "WOSS": {"min": None, "max": None},
}
```

### Step 2.2: `common/session_state.py`

This file manages the lifecycle of the application's state stored in `st.session_state`.

**Action:** Create a new file `common/session_state.py` and add the following code:

```python
# common/session_state.py
import streamlit as st
import os
import shutil
import logging

def initialize_session_state():
    """Initializes all necessary keys in st.session_state with default values."""
    defaults = {
        # Core data
        'header_loader': None,
        'well_data': None,
        'segy_temp_file_path': None,
        'dt': None,
        'trace_count': 0,
        # User selections
        'analysis_mode': 'Single Trace from Well',
        'selected_indices': [],
        'area_selected': False,
        # Configuration and settings
        'plot_settings': {},
        'stats_results': None,
        'normalization_defaults': {},
        # Analysis results
        'loaded_trace_data': [],
        'calculated_descriptors': [],
        'analysis_plots': {},
        'analysis_complete': False,
        # Export
        'export_dir': None,
        'last_zip_buffer': None,
        'last_saved_zip_path': None,
        # GPU state
        'gpu_available': False,
        'gpu_functions': {}
    }
    for key, value in defaults.items():
        st.session_state.setdefault(key, value)


def reset_state():
    """Resets the session state for a new analysis, preserving GPU info."""
    logging.info("Resetting session state for new analysis.")

    # Clean up temporary files and directories
    if st.session_state.get('segy_temp_file_path') and os.path.exists(st.session_state.segy_temp_file_path):
        try:
            os.remove(st.session_state.segy_temp_file_path)
            logging.info(f"Removed temporary file: {st.session_state.segy_temp_file_path}")
        except Exception as e:
            logging.error(f"Error removing temp file {st.session_state.segy_temp_file_path}: {e}")

    if st.session_state.get('export_dir') and os.path.exists(st.session_state.export_dir):
        try:
            shutil.rmtree(st.session_state.export_dir)
            logging.info(f"Removed temporary export directory: {st.session_state.export_dir}")
        except Exception as e:
            logging.error(f"Error removing temp dir {st.session_state.export_dir}: {e}")

    # Store GPU state
    gpu_available = st.session_state.get('gpu_available', False)
    gpu_functions = st.session_state.get('gpu_functions', {})

    # Clear all keys
    st.session_state.clear()

    # Re-initialize the session state
    initialize_session_state()

    # Restore GPU state
    st.session_state.gpu_available = gpu_available
    st.session_state.gpu_functions = gpu_functions

    st.success("State has been reset. You can start a new analysis.")
```

### Step 2.3: `common/ui_elements.py`

This file contains reusable Streamlit UI components to ensure a consistent user experience.

**Action:** Create a new file `common/ui_elements.py` and add the following code:

```python
# common/ui_elements.py
import streamlit as st
import torch

def slider_with_number_input(label, min_value, max_value, value, step, key_base, format=None, help=None):
    """Creates a synchronized slider and number input."""
    c1, c2 = st.columns([3, 1])
    with c1:
        slider_val = st.slider(
            label, min_value, max_value, value, step,
            key=f"{key_base}_slider", format=format, help=help
        )
    with c2:
        number_val = st.number_input(
            "Value", min_value, max_value, slider_val, step,
            key=f"{key_base}_number", label_visibility="collapsed", format=format, help=help
        )
    return number_val

def get_suggested_batch_size():
    """Suggests a batch size based on available GPU memory."""
    if not torch.cuda.is_available():
        return 128  # Default for CPU

    try:
        total_mem = torch.cuda.get_device_properties(0).total_memory / (1024**3)  # VRAM in GB
        if total_mem >= 16:
            return 1024
        elif total_mem >= 8:
            return 512
        elif total_mem >= 4:
            return 256
        else:
            return 128
    except Exception as e:
        st.warning(f"Could not determine GPU memory, using default batch size. Error: {e}")
        return 128

def get_suggested_batch_size_for_export(num_unique_groups):
    """Provides a heuristic for export batch size."""
    if num_unique_groups <= 10:
        return 1
    elif num_unique_groups <= 50:
        return 5
    elif num_unique_groups <= 200:
        return 10
    else:
        return 20
```

---

## Phase 3: Refactor the `utils` Module

This phase isolates all backend logic, data processing, and computations, completely separating them from the UI.

### Step 3.1: `utils/data_utils.py`

This file is for data loading and parsing, stripped of all UI-related code.

**Action:** Overwrite the existing `utils/data_utils.py` with the following completely `tkinter`-free version:

```python
# utils/data_utils.py
import segyio
import numpy as np
import pandas as pd
from tqdm import tqdm
import logging

def merge_segy_batch_files(batch_files, output_file):
    if not batch_files:
        logging.warning("No batch files provided for merging.")
        return False
    logging.info(f"Merging {len(batch_files)} batch files into {output_file}...")
    total_traces = 0
    specs = None
    for batch_file in batch_files:
        try:
            with segyio.open(batch_file, 'r', ignore_geometry=True) as f:
                if specs is None:
                    specs = segyio.tools.metadata(f)
                else:
                    new_specs = segyio.tools.metadata(f)
                    if (len(new_specs.samples) != len(specs.samples) or not np.array_equal(new_specs.samples, specs.samples)):
                        logging.error(f"Incompatible sample specifications in {batch_file}")
                        return False
                total_traces += f.tracecount
        except Exception as e:
            logging.error(f"Error reading batch file {batch_file}: {e}")
            return False
    if specs is None:
        logging.error("Could not obtain specifications from batch files.")
        return False
    specs.tracecount = total_traces
    try:
        with segyio.create(output_file, specs) as dst:
            with segyio.open(batch_files[0], 'r', ignore_geometry=True) as src:
                dst.bin = src.bin
                try:
                    dst.text[0] = src.text[0]
                except:
                    pass
            dst_trace_index = 0
            for batch_file in tqdm(batch_files, desc=f"Merging batch files"):
                with segyio.open(batch_file, 'r', ignore_geometry=True) as src:
                    for src_trace_index in range(src.tracecount):
                        dst.header[dst_trace_index] = src.header[src_trace_index]
                        dst.trace[dst_trace_index] = src.trace[src_trace_index]
                        dst_trace_index += 1
        logging.info(f"Successfully merged {len(batch_files)} files into {output_file} with {total_traces} traces.")
        return True
    except Exception as e:
        logging.error(f"Error during merge: {e}")
        return False

class SegyHeaderLoader:
    def __init__(self, segy_path):
        self.segy_path = segy_path
        self.source_file_path = segy_path

    def load_headers(self, inline_byte, crossline_byte, x_coord_byte, y_coord_byte, scaler_byte, use_custom_scaler=False):
        self.inline_byte = inline_byte
        self.crossline_byte = crossline_byte
        self.x_coord_byte = x_coord_byte
        self.y_coord_byte = y_coord_byte
        self.scaler_byte = scaler_byte
        self.use_custom_scaler = use_custom_scaler
        with segyio.open(self.segy_path, 'r', ignore_geometry=True) as segyfile:
            logging.info("Loading SEG-Y header information...")
            self.inlines = np.array([h[self.inline_byte] for h in tqdm(segyfile.header, desc="Loading inlines")])
            self.crosslines = np.array([h[self.crossline_byte] for h in tqdm(segyfile.header, desc="Loading crosslines")])
            self.original_inline_min = int(np.min(self.inlines))
            self.original_inline_max = int(np.max(self.inlines))
            self.original_xline_min = int(np.min(self.crosslines))
            self.original_xline_max = int(np.max(self.crosslines))
            self.x_coords = np.array([float(h[self.x_coord_byte]) for h in tqdm(segyfile.header, desc="Loading X coords")], dtype=np.float64)
            self.y_coords = np.array([float(h[self.y_coord_byte]) for h in tqdm(segyfile.header, desc="Loading Y coords")], dtype=np.float64)
            try:
                if use_custom_scaler:
                    custom_scaler = float(self.scaler_byte)
                    scaler_val = custom_scaler if custom_scaler >= 0 else 1 / abs(custom_scaler)
                    self.x_coords *= scaler_val
                    self.y_coords *= scaler_val
                else:
                    scalers = np.array([h[self.scaler_byte] for h in segyfile.header])
                    scaler_value = float(next((s for s in scalers if s != 0), 1))
                    scaler_val = scaler_value if scaler_value >= 0 else 1 / abs(scaler_value)
                    self.x_coords *= scaler_val
                    self.y_coords *= scaler_val
            except Exception as e:
                logging.warning(f"Could not apply coordinate scaling: {e}. Using unscaled coordinates.")
        xy = np.column_stack((self.x_coords, self.y_coords))
        _, unique_idx = np.unique(xy, axis=0, return_index=True)
        self.unique_indices = unique_idx
        self.x_coords = self.x_coords[unique_idx]
        self.y_coords = self.y_coords[unique_idx]
        self.inlines = self.inlines[unique_idx]
        self.crosslines = self.crosslines[unique_idx]

    def get_inline_crossline_range(self):
        try:
            return {'inline_min': self.original_inline_min, 'inline_max': self.original_inline_max, 'xline_min': self.original_xline_min, 'xline_max': self.original_xline_max}
        except AttributeError:
            return {'inline_min': 0, 'inline_max': 0, 'xline_min': 0, 'xline_max': 0}

def load_excel_data(excel_file):
    df = pd.read_excel(excel_file)
    expected_cols = ["X", "Y", "Z", "MD", "Surface", "Well"]
    for col in expected_cols:
        if col not in df.columns:
            raise ValueError(f"Missing expected column '{col}' in Excel file.")
    return df

def get_nearest_trace_index(header_loader, well_x, well_y):
    coords = np.column_stack((header_loader.x_coords, header_loader.y_coords))
    distances = np.linalg.norm(coords - np.array([well_x, well_y]), axis=1)
    return header_loader.unique_indices[np.argmin(distances)]

def load_trace_sample(segy_path, trace_index):
    try:
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            if trace_index < 0 or trace_index >= segyfile.tracecount:
                logging.warning(f"Trace index {trace_index} is out of bounds.")
                return None
            return segyfile.trace.raw[trace_index]
    except Exception as e:
        logging.error(f"Failed to load trace {trace_index} from {segy_path}: {e}")
        return None

def get_sampling_interval(segy_path):
    try:
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            return segyio.tools.dt(segyfile) / 1000.0  # Return in ms
    except Exception as e:
        logging.error(f"Could not read sampling interval from {segy_path}: {e}")
        return None

def get_trace_count(segy_path):
    try:
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            return segyfile.tracecount
    except Exception as e:
        logging.error(f"Could not read trace count from {segy_path}: {e}")
        return 0
```

### Step 3.2: `utils/processing.py`

This file contains the core scientific computations, completely separate from any UI.

**Action:** Overwrite the existing `utils/processing.py` with the following `tkinter`-free version:

```python
# utils/processing.py
import numpy as np
import segyio
import logging
from tqdm import tqdm
from .data_utils import load_trace_sample

def calculate_woss(descriptor, plot_settings):
    """
    Calculate WOSS (Weighted-Optimum Spectral Shape) from spectral descriptors.
    
    Args:
        descriptor (dict): Dictionary containing spectral descriptors
        plot_settings (dict): Settings containing epsilon, fdom_exponent, and hfc_p95
        
    Returns:
        np.ndarray: WOSS values
    """
    epsilon = plot_settings.get('epsilon', 1e-4)
    fdom_exponent = plot_settings.get('fdom_exponent', 2.0)
    hfc_p95 = plot_settings.get('hfc_p95', 1.0)
    
    required_keys = ['hfc', 'norm_fdom', 'mag_voice_slope']
    if not all(key in descriptor for key in required_keys):
        logging.warning(f"Missing required keys for WOSS calculation: {required_keys}")
        return np.array([], dtype=np.float32)
    
    # Normalize HFC
    hfc_norm = descriptor['hfc'] / hfc_p95 if hfc_p95 != 0 else descriptor['hfc']
    
    # Calculate denominator
    denominator = hfc_norm * (descriptor['norm_fdom']**fdom_exponent + epsilon)
    
    # Calculate WOSS with error handling
    with np.errstate(divide='ignore', invalid='ignore'):
        woss = np.where(denominator > epsilon, descriptor['mag_voice_slope'] / denominator, 0.0)
        woss[~np.isfinite(woss)] = 0.0
        woss = np.clip(woss, -1e6, 1e6)
    
    return woss.astype(np.float32)

def clean_array(arr):
    """
    Clean an array by removing invalid values and converting to real numbers.
    
    Args:
        arr: Input array
        
    Returns:
        np.ndarray: Cleaned array
    """
    if not isinstance(arr, np.ndarray) or arr.size == 0:
        return np.array([])
    
    if np.iscomplexobj(arr):
        arr = np.real(arr)
    
    return arr[np.isfinite(arr)]

def calculate_stats_and_defaults(segy_path, dt, sample_percent, max_traces_for_stats, 
                                spectral_params, spec_descriptor_func):
    """
    Calculate statistics and default plotting limits from a sample of traces.
    
    Args:
        segy_path (str): Path to SEG-Y file
        dt (float): Sampling interval
        sample_percent (float): Percentage of traces to sample
        max_traces_for_stats (int): Maximum number of traces for statistics
        spectral_params (dict): Parameters for spectral analysis
        spec_descriptor_func (callable): Function to calculate spectral descriptors
        
    Returns:
        dict: Dictionary containing statistics and default limits
    """
    try:
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            num_traces_total = segyfile.tracecount
            
        if num_traces_total == 0:
            logging.warning("SEG-Y file has 0 traces.")
            return None
            
        # Calculate number of traces to sample
        num_traces_to_sample = min(
            max(1, int(num_traces_total * (sample_percent / 100.0))),
            max_traces_for_stats
        )
        num_traces_to_sample = min(num_traces_to_sample, num_traces_total)
        
        # Select random sample of traces
        sample_indices = np.random.choice(num_traces_total, num_traces_to_sample, replace=False)
        
        # Load sample traces
        sample_descriptors = []
        loaded_samples = []
        
        for i in tqdm(sample_indices, desc="Loading sample traces"):
            trace_sample = load_trace_sample(segy_path, i)
            if trace_sample is not None and trace_sample.size > 0:
                loaded_samples.append(trace_sample)
        
        if not loaded_samples:
            logging.error("No valid sample traces could be loaded.")
            return None
        
        # Pad traces to same length
        max_len = max(len(s) for s in loaded_samples)
        
        # Calculate descriptors for each sample
        for trace_sample in tqdm(loaded_samples, desc="Calculating sample descriptors"):
            try:
                if len(trace_sample) < max_len:
                    trace_sample = np.pad(trace_sample, (0, max_len - len(trace_sample)), 'constant')
                
                fmax_calc = (1 / (dt * 2)) * 0.8  # 80% of Nyquist
                descriptor = spec_descriptor_func(trace_sample, dt, fmax=fmax_calc, **spectral_params)
                descriptor['data'] = trace_sample
                sample_descriptors.append(descriptor)
                
            except Exception as e:
                logging.warning(f"Error calculating descriptor for a sample trace: {e}")

        if not sample_descriptors:
            logging.error("Failed to calculate descriptors for any sample traces.")
            return None

        # Calculate statistics
        stats_results = calculate_descriptor_statistics(sample_descriptors)
        percentile_defaults = calculate_percentile_defaults(sample_descriptors, spectral_params)
        
        return {
            'stats': stats_results,
            'defaults': percentile_defaults
        }

    except Exception as e:
        logging.error(f"Error in calculate_stats_and_defaults: {e}")
        return None

def calculate_descriptor_statistics(sample_descriptors):
    """
    Calculate comprehensive statistics for all descriptors.
    
    Args:
        sample_descriptors (list): List of descriptor dictionaries
        
    Returns:
        dict: Statistics for each descriptor
    """
    stats_results = {}
    
    # Define keys to analyze
    stat_keys = ["data", "hfc", "spec_decrease", "spec_slope", "mag_voice_slope", 
                 "spec_bandwidth", "spec_rolloff", "norm_fdom"]
    
    for key in stat_keys:
        # Collect all values for this descriptor
        arr_list = []
        for descriptor in sample_descriptors:
            if key in descriptor and isinstance(descriptor[key], np.ndarray):
                arr_list.append(descriptor[key].flatten())
        
        if arr_list:
            combined_arr = clean_array(np.concatenate(arr_list))
            
            if combined_arr.size > 0:
                stats_results[key] = {
                    'mean': float(np.mean(combined_arr)),
                    'std': float(np.std(combined_arr)),
                    'min': float(np.min(combined_arr)),
                    'max': float(np.max(combined_arr)),
                    'p5': float(np.percentile(combined_arr, 5)),
                    'p25': float(np.percentile(combined_arr, 25)),
                    'p50': float(np.percentile(combined_arr, 50)),
                    'p75': float(np.percentile(combined_arr, 75)),
                    'p95': float(np.percentile(combined_arr, 95)),
                    'count': len(combined_arr)
                }
    
    return stats_results

def calculate_percentile_defaults(sample_descriptors, spectral_params):
    """
    Calculate default percentile values for plotting limits.
    
    Args:
        sample_descriptors (list): List of descriptor dictionaries
        spectral_params (dict): Spectral analysis parameters
        
    Returns:
        dict: Default percentile values
    """
    percentile_defaults = {}
    data_arrays = {}
    
    # Define keys to analyze
    stat_keys = ["data", "hfc", "spec_decrease", "spec_slope", "mag_voice_slope", 
                 "spec_bandwidth", "spec_rolloff", "norm_fdom"]
    
    # Collect arrays for each descriptor
    for key in stat_keys:
        arr_list = []
        for descriptor in sample_descriptors:
            if key in descriptor and isinstance(descriptor[key], np.ndarray):
                arr_list.append(descriptor[key].flatten())
        
        if arr_list:
            data_arrays[key] = clean_array(np.concatenate(arr_list))

    # Calculate percentiles
    for key, arr in data_arrays.items():
        if arr.size > 0:
            p5, p95 = np.percentile(arr, [5, 95])
            percentile_defaults[f"{key}_p5"] = float(p5)
            percentile_defaults[f"{key}_p95"] = float(p95)
            
            # Calculate absolute percentiles for certain descriptors
            if key in ["data", "mag_voice_slope"]:
                percentile_defaults[f"{key}_p95_abs"] = float(np.percentile(np.abs(arr), 95))

    # Calculate WOSS percentiles separately
    woss_settings = {**spectral_params, 'hfc_p95': percentile_defaults.get('hfc_p95', 1.0)}
    woss_values = []
    
    for descriptor in sample_descriptors:
        try:
            woss = calculate_woss(descriptor, woss_settings)
            if woss.size > 0:
                woss_values.append(woss)
        except Exception as e:
            logging.warning(f"Error calculating WOSS for sample: {e}")
    
    if woss_values:
        woss_combined = clean_array(np.concatenate(woss_values))
        if woss_combined.size > 0:
            p5, p95 = np.percentile(woss_combined, [5, 95])
            percentile_defaults['woss_p5'] = float(p5)
            percentile_defaults['woss_p95'] = float(p95)
            percentile_defaults['woss_p95_abs'] = float(np.percentile(np.abs(woss_combined), 95))

    return percentile_defaults

def process_traces_batch(segy_path, trace_indices, dt, spectral_params, spec_descriptor_func, 
                        batch_size=100, progress_callback=None):
    """
    Process multiple traces in batches for memory efficiency.
    
    Args:
        segy_path (str): Path to SEG-Y file
        trace_indices (list): List of trace indices to process
        dt (float): Sampling interval
        spectral_params (dict): Parameters for spectral analysis
        spec_descriptor_func (callable): Function to calculate spectral descriptors
        batch_size (int): Number of traces to process at once
        progress_callback (callable): Optional callback for progress updates
        
    Returns:
        tuple: (trace_data_list, descriptors_list)
    """
    all_trace_data = []
    all_descriptors = []
    
    # Process in batches
    for i in range(0, len(trace_indices), batch_size):
        batch_indices = trace_indices[i:i + batch_size]
        
        # Load batch traces
        batch_traces = []
        for idx in batch_indices:
            trace_sample = load_trace_sample(segy_path, idx)
            if trace_sample is not None:
                batch_traces.append(trace_sample)
        
        if not batch_traces:
            continue
            
        # Find max length for padding
        max_len = max(len(t) for t in batch_traces)
        
        # Process each trace in the batch
        for trace_sample in batch_traces:
            try:
                if len(trace_sample) < max_len:
                    trace_sample = np.pad(trace_sample, (0, max_len - len(trace_sample)), 'constant')
                
                fmax_calc = (1 / (dt * 2)) * 0.8  # 80% of Nyquist
                descriptor = spec_descriptor_func(trace_sample, dt, fmax=fmax_calc, **spectral_params)
                
                all_trace_data.append(trace_sample)
                all_descriptors.append(descriptor)
                
            except Exception as e:
                logging.warning(f"Error processing trace: {e}")
        
        # Update progress if callback provided
        if progress_callback:
            progress = min(1.0, (i + batch_size) / len(trace_indices))
            progress_callback(progress)
    
    return all_trace_data, all_descriptors
```

### Step 3.3: `utils/dlogst_spec_descriptor_cpu.py`

This file contains the CPU implementation of the spectral descriptor calculations as a fallback when GPU is not available.

**Action:** Create a new file `utils/dlogst_spec_descriptor_cpu.py` and add the following code:

```python
# utils/dlogst_spec_descriptor_cpu.py
import numpy as np
from scipy.signal import find_peaks
from scipy.fft import fft, fftfreq
import logging

def dlogst_spec_descriptor_cpu(trace, dt, fmax=None, **kwargs):
    """
    CPU implementation of spectral descriptor calculations.
    
    Args:
        trace: 1D numpy array of seismic trace
        dt: Sampling interval in seconds
        fmax: Maximum frequency for analysis (Hz)
        **kwargs: Additional parameters for spectral analysis
    
    Returns:
        dict: Dictionary containing calculated spectral descriptors
    """
    trace = np.asarray(trace, dtype=np.float32)
    
    # Handle edge cases
    if len(trace) == 0:
        return {}
        
    # Parameters with defaults
    use_band_limited = kwargs.get('use_band_limited', False)
    b1 = kwargs.get('b1', 5.0)
    b2 = kwargs.get('b2', 40.0)
    shape = kwargs.get('shape', 1.0)
    kmax = kwargs.get('kmax', 1.0)
    int_val = kwargs.get('int_val', 1.0)
    p_bandwidth = kwargs.get('p_bandwidth', 0.5)
    roll_percent = kwargs.get('roll_percent', 0.85)
    
    # Calculate spectrum
    spectrum = fft(trace)
    freqs = fftfreq(len(trace), dt)
    
    # Keep only positive frequencies
    n_positive = len(freqs) // 2
    freqs = freqs[:n_positive]
    spectrum = spectrum[:n_positive]
    
    if fmax is not None:
        freq_mask = freqs <= fmax
        freqs = freqs[freq_mask]
        spectrum = spectrum[freq_mask]
    
    # Magnitude spectrum
    magnitude = np.abs(spectrum)
    
    # Avoid division by zero
    magnitude = np.where(magnitude < 1e-10, 1e-10, magnitude)
    
    # Calculate voice (instantaneous frequency)
    voice = np.zeros_like(magnitude)
    if len(magnitude) > 1:
        phase = np.angle(spectrum)
        voice[1:] = np.diff(phase) / (2 * np.pi * dt)
    
    # Logistic transform for voice
    voice_transformed = kmax / (1 + np.exp(-shape * (voice - int_val)))
    
    # Magnitude * Voice
    mag_voice = magnitude * voice_transformed
    
    # Spectral descriptors
    descriptors = {
        'data': trace,
        'magnitude': magnitude,
        'voice': voice_transformed,
        'mag_voice': mag_voice
    }
    
    # Calculate slopes
    if use_band_limited and len(freqs) > 1:
        freq_mask = (freqs >= b1) & (freqs <= b2)
        if np.any(freq_mask):
            f_band = freqs[freq_mask]
            mag_band = magnitude[freq_mask]
            voice_band = voice_transformed[freq_mask]
            
            # Spectral slope
            if len(f_band) > 1:
                descriptors['spec_slope'] = calculate_slope_cpu(f_band, mag_band)
            
            # Mag*Voice slope
            if len(f_band) > 1:
                descriptors['mag_voice_slope'] = calculate_slope_cpu(f_band, mag_band * voice_band)
    else:
        # Full band
        if len(freqs) > 1:
            descriptors['spec_slope'] = calculate_slope_cpu(freqs, magnitude)
            descriptors['mag_voice_slope'] = calculate_slope_cpu(freqs, mag_voice)
    
    # Spectral decrease
    descriptors['spec_decrease'] = calculate_decrease_cpu(magnitude)
    
    # High Frequency Content (HFC)
    descriptors['hfc'] = calculate_hfc_cpu(freqs, magnitude)
    
    # Spectral bandwidth
    descriptors['spec_bandwidth'] = calculate_bandwidth_cpu(freqs, magnitude, p_bandwidth)
    
    # Spectral rolloff
    descriptors['spec_rolloff'] = calculate_rolloff_cpu(freqs, magnitude, roll_percent)
    
    # Normalized dominant frequency
    descriptors['norm_fdom'] = calculate_norm_fdom_cpu(freqs, magnitude)
    
    return descriptors

def calculate_slope_cpu(freqs, signal):
    """Calculate spectral slope using least squares fit."""
    if len(freqs) < 2:
        return np.float32(0.0)
    
    # Use log scale to avoid numerical issues
    log_freqs = np.log(freqs + 1e-10)
    log_signal = np.log(signal + 1e-10)
    
    # Least squares fit
    n = len(log_freqs)
    sum_x = np.sum(log_freqs)
    sum_y = np.sum(log_signal)
    sum_xy = np.sum(log_freqs * log_signal)
    sum_x2 = np.sum(log_freqs * log_freqs)
    
    denominator = n * sum_x2 - sum_x * sum_x
    if abs(denominator) < 1e-10:
        return np.float32(0.0)
    
    slope = (n * sum_xy - sum_x * sum_y) / denominator
    return np.float32(slope)

def calculate_decrease_cpu(signal):
    """Calculate spectral decrease."""
    if len(signal) <= 1:
        return np.float32(0.0)
    
    k = np.arange(1, len(signal))
    numerator = np.sum((signal[1:] - signal[0]) / k)
    denominator = np.sum(signal)
    
    if denominator < 1e-10:
        return np.float32(0.0)
    
    return np.float32(numerator / denominator)

def calculate_hfc_cpu(freqs, magnitude):
    """Calculate High Frequency Content."""
    if len(freqs) == 0:
        return np.float32(0.0)
    
    # Weight by frequency
    hfc = np.sum(freqs * magnitude)
    return np.float32(hfc)

def calculate_bandwidth_cpu(freqs, magnitude, p):
    """Calculate spectral bandwidth."""
    if len(freqs) == 0:
        return np.float32(0.0)
    
    # Centroid
    total_power = np.sum(magnitude)
    if total_power < 1e-10:
        return np.float32(0.0)
    
    centroid = np.sum(freqs * magnitude) / total_power
    
    # Bandwidth
    bandwidth = np.sum(((freqs - centroid) ** p) * magnitude) / total_power
    return np.float32(bandwidth ** (1/p))

def calculate_rolloff_cpu(freqs, magnitude, roll_percent):
    """Calculate spectral rolloff."""
    if len(freqs) == 0:
        return np.float32(0.0)
    
    cumsum = np.cumsum(magnitude)
    total = cumsum[-1]
    
    if total < 1e-10:
        return np.float32(0.0)
    
    threshold = total * roll_percent
    rolloff_idx = np.where(cumsum >= threshold)[0]
    
    if len(rolloff_idx) == 0:
        return freqs[-1]
    
    return np.float32(freqs[rolloff_idx[0]])

def calculate_norm_fdom_cpu(freqs, magnitude):
    """Calculate normalized dominant frequency."""
    if len(freqs) == 0:
        return np.float32(0.0)
    
    # Find peaks
    peaks, _ = find_peaks(magnitude, height=np.max(magnitude) * 0.1)
    
    if len(peaks) == 0:
        # Use maximum as dominant frequency
        max_idx = np.argmax(magnitude)
        fdom = freqs[max_idx]
    else:
        # Use highest peak
        peak_heights = magnitude[peaks]
        max_peak_idx = peaks[np.argmax(peak_heights)]
        fdom = freqs[max_peak_idx]
    
    # Normalize by Nyquist frequency
    nyquist = freqs[-1] if len(freqs) > 0 else 1.0
    return np.float32(fdom / nyquist)
```

### Step 3.4: `utils/gpu_utils.py`

This file handles GPU initialization and provides a unified interface for GPU functions.

**Action:** Create a new file `utils/gpu_utils.py` and add the following code:

```python
# utils/gpu_utils.py
import logging

def initialize_gpu():
    """
    Initialize GPU functionality and return available GPU functions.
    
    Returns:
        tuple: (gpu_available: bool, gpu_functions: dict)
    """
    try:
        import cupy as cp
        import torch
        
        # Check if CUDA is available
        if not torch.cuda.is_available():
            logging.info("CUDA not available through PyTorch")
            return False, {}
        
        # Check if CuPy can access GPU
        try:
            cp.cuda.Device(0).use()
            logging.info("GPU initialized successfully")
            
            # Import GPU functions
            from dlogst_spec_descriptor_gpu import (
                dlogst_spec_descriptor_gpu,
                dlogst_spec_descriptor_gpu_2d_chunked
            )
            
            gpu_functions = {
                'single_trace': dlogst_spec_descriptor_gpu,
                'batch_traces': dlogst_spec_descriptor_gpu_2d_chunked
            }
            
            return True, gpu_functions
            
        except Exception as e:
            logging.warning(f"GPU initialization failed: {e}")
            return False, {}
            
    except ImportError as e:
        logging.info(f"GPU libraries not available: {e}")
        return False, {}
    except Exception as e:
        logging.error(f"Unexpected error during GPU initialization: {e}")
        return False, {}

def get_gpu_memory_info():
    """Get GPU memory information if available."""
    try:
        import torch
        if torch.cuda.is_available():
            device = torch.cuda.current_device()
            total_memory = torch.cuda.get_device_properties(device).total_memory
            allocated_memory = torch.cuda.memory_allocated(device)
            cached_memory = torch.cuda.memory_reserved(device)
            
            return {
                'total_memory_gb': total_memory / (1024**3),
                'allocated_memory_gb': allocated_memory / (1024**3),
                'cached_memory_gb': cached_memory / (1024**3),
                'free_memory_gb': (total_memory - allocated_memory) / (1024**3)
            }
    except Exception as e:
        logging.warning(f"Could not get GPU memory info: {e}")
    
    return None
```

### Step 3.5: `utils/general_utils.py`

This file contains miscellaneous helper functions that don't fit in other, more specific utility modules.

**Action:** Create a new file `utils/general_utils.py` and add the following code:

```python
# utils/general_utils.py
import numpy as np
import math
from tqdm import tqdm

def parse_polyline_string(polyline_str):
    """
    Parses a string of coordinates into a list of (x, y) tuples.
    Handles various delimiters like spaces, commas, and newlines.
    """
    points = []
    # Replace commas and semicolons with spaces, then split by any whitespace
    parts = polyline_str.replace(',', ' ').replace(';', ' ').split()
    if len(parts) % 2 != 0:
        raise ValueError("Polyline string must contain an even number of coordinates (pairs of X and Y).")
    for i in range(0, len(parts), 2):
        try:
            x = float(parts[i])
            y = float(parts[i+1])
            points.append((x, y))
        except ValueError:
            raise ValueError(f"Could not parse coordinate pair: '{parts[i]}', '{parts[i+1]}'")
    return points

def distance_point_to_segment(px, py, x1, y1, x2, y2):
    """
    Calculate the minimum distance from a point (px, py) to a line segment ((x1, y1), (x2, y2)).
    """
    line_mag_sq = (x2 - x1)**2 + (y2 - y1)**2
    if line_mag_sq < 1e-9: # Segment is a point
        return math.sqrt((px - x1)**2 + (py - y1)**2)
    
    u = ((px - x1) * (x2 - x1) + (py - y1) * (y2 - y1)) / line_mag_sq
    
    if u < 0.0 or u > 1.0:
        # Closest point is one of the endpoints
        dist1 = math.sqrt((px - x1)**2 + (py - y1)**2)
        dist2 = math.sqrt((px - x2)**2 + (py - y2)**2)
        return min(dist1, dist2)
    else:
        # Closest point is on the segment
        ix = x1 + u * (x2 - x1)
        iy = y1 + u * (y2 - y1)
        return math.sqrt((px - ix)**2 + (py - iy)**2)

def find_traces_near_polyline(header_loader, polyline_points, tolerance):
    """
    Finds all trace indices within a given tolerance of a polyline.
    """
    trace_coords = np.column_stack((header_loader.x_coords, header_loader.y_coords))
    nearby_indices = []

    for i in tqdm(range(len(trace_coords)), desc="Finding traces near polyline"):
        px, py = trace_coords[i]
        min_dist = float('inf')
        
        for j in range(len(polyline_points) - 1):
            x1, y1 = polyline_points[j]
            x2, y2 = polyline_points[j+1]
            dist = distance_point_to_segment(px, py, x1, y1, x2, y2)
            if dist < min_dist:
                min_dist = dist
        
        if min_dist <= tolerance:
            nearby_indices.append(header_loader.unique_indices[i])
            
    return nearby_indices
```

### Step 3.6: `utils/visualization.py`

This file will contain all plotting functions, keeping the main application logic clean.

**Action:** Create a new file `utils/visualization.py` and add the following code:

```python
# utils/visualization.py
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
import plotly.express as px
from common.constants import REVERSE_ATTR_NAME_MAP

def plot_basemap_with_wells(header_loader, well_data=None):
    """Plots the seismic basemap and optional well locations."""
    fig = go.Figure()

    # Add seismic trace locations
    fig.add_trace(go.Scatter(
        x=header_loader.x_coords,
        y=header_loader.y_coords,
        mode='markers',
        marker=dict(size=2, color='blue', opacity=0.6),
        name='Seismic Traces',
        hovertemplate='X: %{x}<br>Y: %{y}<extra></extra>'
    ))

    # Add well locations if available
    if well_data is not None and not well_data.empty:
        fig.add_trace(go.Scatter(
            x=well_data['X'],
            y=well_data['Y'],
            mode='markers+text',
            marker=dict(size=8, color='red', symbol='triangle-up'),
            text=well_data['Well'],
            textposition="top center",
            name='Wells',
            hovertemplate='Well: %{text}<br>X: %{x}<br>Y: %{y}<extra></extra>'
        ))

    fig.update_layout(
        title="Seismic Basemap",
        xaxis_title="X Coordinate",
        yaxis_title="Y Coordinate",
        legend_title="Legend",
        height=600,
        showlegend=True
    )
    fig.update_yaxes(scaleanchor="x", scaleratio=1)
    return fig

def plot_trace_with_descriptors(trace_data, descriptors, dt, plot_settings, selected_outputs):
    """
    Creates a multi-panel plot for a single trace and its calculated descriptors.
    """
    num_plots = len(selected_outputs)
    if num_plots == 0:
        return go.Figure()

    # Create subplot layout
    cols = min(3, num_plots)  # Max 3 columns
    rows = (num_plots + cols - 1) // cols  # Ceiling division
    
    fig = make_subplots(
        rows=rows, cols=cols,
        subplot_titles=selected_outputs,
        shared_yaxes=True,
        vertical_spacing=0.1,
        horizontal_spacing=0.1
    )
    
    time_vector = np.arange(len(trace_data)) * dt * 1000  # Convert to milliseconds

    for i, output_name in enumerate(selected_outputs):
        row = (i // cols) + 1
        col = (i % cols) + 1
        
        if output_name == "Input Signal":
            fig.add_trace(
                go.Scatter(
                    x=trace_data, y=time_vector,
                    name=output_name,
                    mode='lines',
                    line=dict(width=1)
                ),
                row=row, col=col
            )
            fig.update_xaxes(title_text="Amplitude", row=row, col=col)
            
        elif output_name == "Magnitude Spectrogram":
            if 'magnitude' in descriptors:
                freq_vector = np.fft.fftfreq(len(trace_data), dt)[:len(descriptors['magnitude'])]
                # Create 2D spectrogram (time vs frequency)
                spectrogram = np.abs(np.fft.fft(trace_data.reshape(-1, 1), axis=0))[:len(freq_vector)]
                fig.add_trace(
                    go.Heatmap(
                        z=spectrogram,
                        y=freq_vector,
                        x=time_vector,
                        colorscale='Viridis',
                        name=output_name
                    ),
                    row=row, col=col
                )
                fig.update_xaxes(title_text="Time (ms)", row=row, col=col)
                fig.update_yaxes(title_text="Frequency (Hz)", row=row, col=col)
            
        elif output_name == "Magnitude * Voice":
            if 'mag_voice' in descriptors:
                fig.add_trace(
                    go.Scatter(
                        x=descriptors['mag_voice'], y=time_vector,
                        name=output_name,
                        mode='lines',
                        line=dict(width=1, color='orange')
                    ),
                    row=row, col=col
                )
                fig.update_xaxes(title_text="Magnitude*Voice", row=row, col=col)
        
        else:
            # Handle other descriptors
            internal_name = get_internal_name(output_name)
            if internal_name in descriptors:
                data_to_plot = descriptors[internal_name]
                if isinstance(data_to_plot, np.ndarray):
                    if data_to_plot.ndim == 1:
                        # 1D descriptor
                        if len(data_to_plot) == len(time_vector):
                            fig.add_trace(
                                go.Scatter(
                                    x=data_to_plot, y=time_vector,
                                    name=output_name,
                                    mode='lines',
                                    line=dict(width=1)
                                ),
                                row=row, col=col
                            )
                        else:
                            # Frequency domain data
                            freq_vector = np.fft.fftfreq(len(trace_data), dt)[:len(data_to_plot)]
                            fig.add_trace(
                                go.Scatter(
                                    x=freq_vector, y=data_to_plot,
                                    name=output_name,
                                    mode='lines',
                                    line=dict(width=1)
                                ),
                                row=row, col=col
                            )
                            fig.update_xaxes(title_text="Frequency (Hz)", row=row, col=col)
                    else:
                        # 2D descriptor (spectrogram)
                        fig.add_trace(
                            go.Heatmap(
                                z=data_to_plot,
                                y=time_vector,
                                colorscale='RdBu',
                                zmid=0,
                                name=output_name
                            ),
                            row=row, col=col
                        )
                        fig.update_xaxes(title_text="Frequency Bin", row=row, col=col)
                
                fig.update_xaxes(title_text=output_name, row=row, col=col)

    # Update layout
    fig.update_layout(
        title_text="Single Trace Analysis",
        height=300 * rows,
        showlegend=False
    )
    
    # Update y-axes to show time in reverse (depth-like display)
    for i in range(1, rows + 1):
        for j in range(1, cols + 1):
            fig.update_yaxes(
                title_text="Time (ms)" if j == 1 else "",
                autorange="reversed",
                row=i, col=j
            )

    return fig

def plot_descriptor_section(trace_data_list, descriptors_list, dt, plot_settings, output_name):
    """
    Creates a 2D section plot for a single descriptor across multiple traces.
    """
    if not descriptors_list:
        return go.Figure()
        
    internal_name = get_internal_name(output_name)
    
    if output_name == "Input Signal":
        section_data = trace_data_list
    else:
        section_data = []
        for desc_dict in descriptors_list:
            if internal_name in desc_dict:
                data = desc_dict[internal_name]
                if isinstance(data, np.ndarray) and data.ndim == 1:
                    section_data.append(data)
                elif isinstance(data, (int, float)):
                    # Scalar descriptor - create array
                    section_data.append(np.full(len(trace_data_list[0]), data))

    if not section_data:
        return go.Figure()

    # Pad traces to the same length
    max_len = max(len(t) for t in section_data)
    padded_section = np.array([
        np.pad(t, (0, max_len - len(t)), 'constant', constant_values=0)
        for t in section_data
    ])

    time_vector = np.arange(max_len) * dt * 1000  # Convert to milliseconds
    trace_numbers = np.arange(len(section_data))

    # Apply plot limits if available
    vmin = plot_settings.get(f"{internal_name}_p5", None)
    vmax = plot_settings.get(f"{internal_name}_p95", None)
    
    if vmin is None or vmax is None:
        vmin = np.percentile(padded_section, 5)
        vmax = np.percentile(padded_section, 95)

    fig = go.Figure(data=go.Heatmap(
        z=padded_section.T,
        x=trace_numbers,
        y=time_vector,
        colorscale='RdBu',
        zmid=0,
        zmin=vmin,
        zmax=vmax,
        colorbar=dict(title=output_name)
    ))

    fig.update_layout(
        title=f"Section Plot: {output_name}",
        xaxis_title="Trace Number",
        yaxis_title="Time (ms)",
        height=600
    )
    fig.update_yaxes(autorange="reversed")
    return fig

def plot_interactive_basemap(header_loader, well_data=None):
    """
    Creates an interactive basemap for polyline selection.
    """
    fig = plot_basemap_with_wells(header_loader, well_data)
    
    # Add instructions
    fig.add_annotation(
        text="Click points to define polyline",
        xref="paper", yref="paper",
        x=0.02, y=0.98,
        showarrow=False,
        bgcolor="white",
        bordercolor="black",
        borderwidth=1
    )
    
    return fig

def get_internal_name(display_name):
    """Convert display name to internal attribute name."""
    from common.constants import ATTR_NAME_MAP
    return ATTR_NAME_MAP.get(display_name, display_name.lower().replace(' ', '_'))
```

### Step 3.6: `utils/export_utils.py`

A small helper module for functions related to the data export process.

**Action:** Overwrite the existing `utils/export_utils.py` with the following code:

```python
# utils/export_utils.py
import numpy as np

def select_export_attributes(descriptor_dict, desired_attributes):
    """
    Filters a list of desired export attributes against what is available
    in a calculated descriptor dictionary.
    
    Args:
        descriptor_dict (dict): A dictionary containing calculated descriptor arrays.
        desired_attributes (list): A list of internal attribute names to export.
        
    Returns:
        dict: A dictionary with keys as attribute names and values as the data arrays,
              containing only the attributes that were both desired and available.
    """
    available_for_export = {}
    for attr in desired_attributes:
        if attr in descriptor_dict and isinstance(descriptor_dict[attr], np.ndarray):
            available_for_export[attr] = descriptor_dict[attr]
    return available_for_export
```

---

## Phase 4: Create Main App and Pages

This is the final phase, where we build the Streamlit user interface by creating the main `app.py` and the individual page files.

### Step 4.1: `app.py` (Main Entry Point)

This file acts as the main router. It does not render any pages itself but sets up the application and provides a consistent entry point.

**Action:** Create a new file named `app.py` in the root directory and add the following code:

```python
# app.py
import streamlit as st
from common import constants, session_state
from utils import gpu_utils
import logging
import os

# --- Basic Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

st.set_page_config(
    page_title=constants.APP_TITLE,
    page_icon="🌊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# --- CSS Styling ---
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f4e79;
        margin-bottom: 1rem;
    }
    .step-header {
        font-size: 1.5rem;
        color: #2c5282;
        margin-bottom: 0.5rem;
    }
    .info-box {
        background-color: #e6f3ff;
        border-left: 4px solid #1f4e79;
        padding: 1rem;
        margin: 1rem 0;
    }
    .warning-box {
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 1rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# --- Title ---
st.markdown('<div class="main-header">🌊 WOSS Seismic Analysis Tool</div>', unsafe_allow_html=True)

# --- Initialization ---
if 'session_initialized' not in st.session_state:
    session_state.initialize_session_state()
    
    # Check for GPU and store result in session state
    gpu_available, gpu_funcs = gpu_utils.initialize_gpu()
    st.session_state.gpu_available = gpu_available
    st.session_state.gpu_functions = gpu_funcs
    
    if gpu_available:
        st.sidebar.success("🚀 GPU acceleration enabled")
    else:
        st.sidebar.info("💻 Using CPU processing")

    st.session_state.session_initialized = True

# --- Sidebar Navigation ---
st.sidebar.markdown("### 📋 Workflow Navigation")

# Progress tracking
progress_items = [
    ("📥 Load Data", st.session_state.get('header_loader') is not None),
    ("⚙️ Configure", st.session_state.get('plot_settings') is not None),
    ("🎯 Select Area", st.session_state.get('area_selected', False)),
    ("🔬 Analyze", st.session_state.get('analysis_complete', False)),
    ("📊 Export", st.session_state.get('export_complete', False))
]

for item_name, is_complete in progress_items:
    status_icon = "✅" if is_complete else "⏳"
    st.sidebar.markdown(f"{status_icon} {item_name}")

st.sidebar.markdown("---")

# System Information
with st.sidebar.expander("💻 System Information"):
    st.write(f"**GPU Available:** {'Yes' if st.session_state.gpu_available else 'No'}")
    if st.session_state.gpu_available:
        gpu_info = gpu_utils.get_gpu_memory_info()
        if gpu_info:
            st.write(f"**GPU Memory:** {gpu_info['total_memory_gb']:.1f} GB")
            st.write(f"**Available:** {gpu_info['free_memory_gb']:.1f} GB")

# Reset button
if st.sidebar.button("🔄 Start New Analysis", use_container_width=True, type="secondary"):
    session_state.reset_state()
    st.rerun()

# --- Main Content ---
st.markdown("## Welcome to the WOSS Seismic Analysis Tool")

col1, col2 = st.columns([2, 1])

with col1:
    st.markdown("""
    This application provides advanced spectral analysis capabilities for seismic data:
    
    - **Multi-format Support**: Load SEG-Y files and well data
    - **GPU Acceleration**: Utilize CUDA for faster processing
    - **Interactive Visualization**: Explore data with modern plotting tools
    - **Flexible Analysis**: Single trace, inline, crossline, or full volume analysis
    - **Export Capabilities**: Save results in multiple formats
    """)

with col2:
    st.image("https://via.placeholder.com/300x200?text=Seismic+Analysis", caption="WOSS Analysis Workflow")

# --- Current Status ---
st.markdown("---")
st.markdown("## 📊 Current Status")

if st.session_state.get('header_loader'):
    st.success("✅ Data loaded successfully!")
    info = st.session_state.header_loader.get_inline_crossline_range()
    
    col1, col2, col3, col4 = st.columns(4)
    col1.metric("📏 Inline Range", f"{info['inline_min']}-{info['inline_max']}")
    col2.metric("📐 Crossline Range", f"{info['xline_min']}-{info['xline_max']}")
    col3.metric("🔢 Total Traces", f"{st.session_state.trace_count:,}")
    col4.metric("⏱️ Sample Rate", f"{st.session_state.dt*1000:.1f} ms")
    
    if st.session_state.get('area_selected'):
        st.info(f"🎯 Selected {len(st.session_state.selected_indices)} traces for analysis")
    
    if st.session_state.get('analysis_complete'):
        st.success("🔬 Analysis completed successfully!")
        
else:
    st.markdown('<div class="info-box">ℹ️ <strong>Getting Started:</strong> Please navigate to <strong>1_Load_Data</strong> to begin by uploading your SEG-Y file.</div>', unsafe_allow_html=True)

# --- Quick Start Guide ---
with st.expander("🚀 Quick Start Guide"):
    st.markdown("""
    ### Step-by-Step Process:
    
    1. **📥 Load Data**: Upload your SEG-Y file and optional well data
    2. **⚙️ Configure**: Set analysis parameters and calculate statistics
    3. **🎯 Select Area**: Choose traces for analysis (single trace, lines, or volume)
    4. **🔬 Analyze**: Run the spectral analysis calculations
    5. **📊 Export**: Save results and generate reports
    
    ### Tips:
    - Use the sidebar navigation to move between steps
    - GPU acceleration will automatically be used if available
    - Statistics calculation helps set optimal display ranges
    - Large datasets are processed in batches for memory efficiency
    """)

# --- Footer ---
st.markdown("---")
st.markdown("*WOSS Tool v2.0 - Advanced Seismic Spectral Analysis*")
```

### Step 4.2: `pages/1_load_data.py`

This page handles the uploading of SEG-Y and well data files.

**Action:** Create a new file `pages/1_load_data.py` and add the following code:

```python
# pages/1_load_data.py
import streamlit as st
from utils.data_utils import SegyHeaderLoader, load_excel_data, get_sampling_interval, get_trace_count
import tempfile
import os
import logging

st.set_page_config(page_title="Load Data", layout="wide")
st.title("Step 1: Load Data")

# --- Functions ---
@st.cache_data
def load_segy_headers_cached(segy_file_path, inline_byte, xline_byte, x_byte, y_byte, scaler_byte, use_custom_scaler):
    """Cached function to load SEG-Y headers."""
    header_loader = SegyHeaderLoader(segy_file_path)
    header_loader.load_headers(inline_byte, xline_byte, x_byte, y_byte, scaler_byte, use_custom_scaler)
    return header_loader

@st.cache_data
def load_excel_data_cached(uploaded_file):
    """Cached function to load well data from Excel."""
    return load_excel_data(uploaded_file)

# --- UI ---
st.sidebar.header("Data Input")
segy_file = st.sidebar.file_uploader("Upload SEG-Y File", type=['sgy', 'segy'])
well_file = st.sidebar.file_uploader("Upload Well Data (Optional Excel)", type=['xlsx'])

st.sidebar.header("SEG-Y Header Configuration")
c1, c2 = st.sidebar.columns(2)
inline_byte = c1.number_input("Inline Byte", value=189)
xline_byte = c2.number_input("Crossline Byte", value=193)
x_byte = c1.number_input("X-Coord Byte", value=73)
y_byte = c2.number_input("Y-Coord Byte", value=77)

scaler_mode = st.sidebar.radio("Coordinate Scaler", ["Use Scaler Byte", "Use Custom Scaler"], index=0)
if scaler_mode == "Use Scaler Byte":
    scaler_byte = st.sidebar.number_input("Scaler Byte", value=71)
    use_custom_scaler = False
else:
    scaler_byte = st.sidebar.number_input("Custom Scaler Value", value=1.0)
    use_custom_scaler = True

# --- Processing ---
if st.sidebar.button("Load Data", use_container_width=True, type="primary"):
    if segy_file is not None:
        with st.spinner("Processing SEG-Y file..."):
            # Create a temporary file to store the uploaded data
            with tempfile.NamedTemporaryFile(delete=False, suffix=".sgy") as tmp_file:
                tmp_file.write(segy_file.getvalue())
                st.session_state.segy_temp_file_path = tmp_file.name

            try:
                # Load headers using the cached function
                header_loader = load_segy_headers_cached(
                    st.session_state.segy_temp_file_path, inline_byte, xline_byte,
                    x_byte, y_byte, scaler_byte, use_custom_scaler
                )
                st.session_state.header_loader = header_loader

                # Get metadata
                st.session_state.dt = get_sampling_interval(st.session_state.segy_temp_file_path)
                st.session_state.trace_count = get_trace_count(st.session_state.segy_temp_file_path)

                st.success("SEG-Y file headers loaded successfully!")

                # Load well data if provided
                if well_file is not None:
                    st.session_state.well_data = load_excel_data_cached(well_file)
                    st.success("Well data loaded successfully!")
                
            except Exception as e:
                st.error(f"An error occurred while loading the SEG-Y file: {e}")
                logging.error(f"SEG-Y loading error: {e}", exc_info=True)

    else:
        st.sidebar.warning("Please upload a SEG-Y file.")

# --- Display Info ---
if st.session_state.get('header_loader'):
    st.subheader("Loaded Data Summary")
    info = st.session_state.header_loader.get_inline_crossline_range()
    st.write(f"**Inlines:** {info['inline_min']} to {info['inline_max']}")
    st.write(f"**Crosslines:** {info['xline_min']} to {info['xline_max']}")
    st.write(f"**Total Traces:** {st.session_state.trace_count}")
    st.write(f"**Sampling Interval:** {st.session_state.dt * 1000} ms")

    if st.session_state.get('well_data') is not None:
        st.write("**Well Data:**")
        st.dataframe(st.session_state.well_data.head())
else:
    st.info("Upload a SEG-Y file and click 'Load Data' to begin.")
```

### Step 4.3: `pages/2_configure_display.py`

This page allows the user to configure all parameters for the analysis and visualization.

**Action:** Create a new file `pages/2_configure_display.py` and add the following code:

```python
# pages/2_configure_display.py
import streamlit as st
from common import ui_elements
from utils import processing, visualization
import logging

st.set_page_config(page_title="Configure Parameters", layout="wide")
st.title("Step 2: Configure Analysis Parameters")

if 'header_loader' not in st.session_state or st.session_state.header_loader is None:
    st.warning("Please load data in Step 1 before configuring parameters.")
    st.stop()

# --- Display Basemap and SEGY Info ---
with st.expander("Basemap & SEGY Info", expanded=True):
    info = st.session_state.header_loader.get_inline_crossline_range()
    c1, c2, c3, c4 = st.columns(4)
    c1.metric("Min Inline", info['inline_min'])
    c2.metric("Max Inline", info['inline_max'])
    c3.metric("Min Crossline", info['xline_min'])
    c4.metric("Max Crossline", info['xline_max'])

    if st.button("Show Basemap"):
        with st.spinner("Generating basemap..."):
            fig = visualization.plot_basemap_with_wells(
                st.session_state.header_loader,
                st.session_state.get('well_data')
            )
            st.plotly_chart(fig, use_container_width=True)

# --- Parameter Configuration ---
st.header("Parameter Configuration")

# Tabs for different settings
tab_general, tab_spectral, tab_stats, tab_display = st.tabs(["General", "Spectral", "Statistics", "Display Limits"])

with tab_general:
    st.subheader("General Settings")
    st.session_state.plot_settings['epsilon'] = st.number_input(
        "Epsilon (for numerical stability)", value=1e-10, format="%e"
    )
    st.session_state.plot_settings['fdom_exponent'] = st.number_input(
        "Dominant Frequency Exponent (for WOSS)", value=2.0, step=0.1
    )

with tab_spectral:
    st.subheader("Spectral Descriptor Settings")
    st.session_state.plot_settings['use_band_limited'] = st.checkbox("Use Band-Limited for Spectral Slope/Decrease", value=False)
    c1, c2 = st.columns(2)
    st.session_state.plot_settings['b1'] = c1.number_input("b1 (Hz)", value=5.0)
    st.session_state.plot_settings['b2'] = c2.number_input("b2 (Hz)", value=40.0)

    st.subheader("Logistic Transform Settings")
    c1, c2, c3 = st.columns(3)
    st.session_state.plot_settings['kmax'] = c1.number_input("kmax", value=120.0)
    st.session_state.plot_settings['shape'] = c2.number_input("shape", value=0.35)
    st.session_state.plot_settings['int_val'] = c3.number_input("int_val", value=35.0)

    st.subheader("Bandwidth and Rolloff Settings")
    c1, c2 = st.columns(2)
    st.session_state.plot_settings['p_bandwidth'] = c1.number_input("p_bandwidth", value=2.0)
    st.session_state.plot_settings['roll_percent'] = c2.slider("roll_percent", 0.0, 1.0, 0.85)

with tab_stats:
    st.subheader("Statistics Calculation Settings")
    sample_percent = st.slider("Sample Percentage for Stats", 0.1, 100.0, 1.0, 0.1)
    max_traces_for_stats = st.number_input("Max Traces for Stats", value=50)

    if st.button("Calculate Statistics and Default Plot Limits", type="primary"):
        with st.spinner("Calculating statistics from sample traces... This may take a moment."):
            spectral_params = {
                'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                'shape': st.session_state.plot_settings.get('shape', 1.0),
                'kmax': st.session_state.plot_settings.get('kmax', 1.0),
                'int_val': st.session_state.plot_settings.get('int_val', 1.0),
                'b1': st.session_state.plot_settings.get('b1', 0.01),
                'b2': st.session_state.plot_settings.get('b2', 0.1),
                'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 0.5),
                'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.85),
            }
            
            # Determine which spectral descriptor function to use
            spec_descriptor_func = st.session_state.gpu_functions.get('single_trace')
            if not spec_descriptor_func:
                from dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu
                spec_descriptor_func = dlogst_spec_descriptor_cpu

            results = processing.calculate_stats_and_defaults(
                st.session_state.segy_temp_file_path,
                st.session_state.dt,
                sample_percent,
                max_traces_for_stats,
                spectral_params,
                spec_descriptor_func
            )
            if results:
                st.session_state.stats_results = results.get('stats')
                st.session_state.normalization_defaults = results.get('defaults')
                st.success("Statistics calculated successfully!")
                # Apply defaults to plot settings
                st.session_state.plot_settings.update(st.session_state.normalization_defaults)
            else:
                st.error("Failed to calculate statistics.")

with tab_display:
    st.subheader("Plot Display Limits")
    if st.session_state.get('normalization_defaults'):
        st.info("Default limits have been populated from statistics. You can adjust them below.")
    
    defaults = st.session_state.get('normalization_defaults', {})
    
    c1, c2 = st.columns(2)
    st.session_state.plot_settings['data_p5'] = c1.number_input("Input Signal Min (P5)", value=float(defaults.get('data_p5', -1.0)))
    st.session_state.plot_settings['data_p95'] = c2.number_input("Input Signal Max (P95)", value=float(defaults.get('data_p95', 1.0)))
    
    st.session_state.plot_settings['woss_p5'] = c1.number_input("WOSS Min (P5)", value=float(defaults.get('woss_p5', -1.0)))
    st.session_state.plot_settings['woss_p95'] = c2.number_input("WOSS Max (P95)", value=float(defaults.get('woss_p95', 1.0)))
    
    # You can add more limit controls for other descriptors as needed

if st.session_state.get('stats_results'):
    with st.expander("View Calculated Statistics"):
        st.json(st.session_state.stats_results)

st.markdown("---")
if st.button("Confirm Settings and Proceed", use_container_width=True):
    st.success("Parameters have been configured.")
    st.balloons()

```

### Step 4.4: `pages/3_select_area.py`

This page handles the UI for selecting the specific area or traces for analysis.

**Action:** Create a new file `pages/3_select_area.py` and add the following code:

```python
# pages/3_select_area.py
import streamlit as st
from utils import general_utils, data_utils
import logging
import pandas as pd

st.set_page_config(page_title="Select Area", layout="wide")
st.title("Step 3: Select Area for Analysis")

if 'header_loader' not in st.session_state or st.session_state.header_loader is None:
    st.warning("Please load data in Step 1 before selecting an area.")
    st.stop()

# --- Mode Selection ---
st.session_state.analysis_mode = st.sidebar.selectbox(
    "Select Analysis Mode",
    ("Single Trace from Well", "Single Inline", "Single Crossline", "Traces near Polyline", "Full Volume AOI")
)

# --- UI for selected mode ---
mode = st.session_state.analysis_mode

if mode == "Single Trace from Well":
    st.header("Select Well Marker")
    if st.session_state.get('well_data') is not None:
        well_data = st.session_state.well_data
        well_marker_list = [f"{row['Well']} - {row['Surface']}" for _, row in well_data.iterrows()]
        selected_pair_str = st.selectbox("Select a Well-Marker pair:", well_marker_list)
        
        if st.button("Find Nearest Trace", type="primary"):
            selected_row = well_data[well_data.apply(lambda r: f"{r['Well']} - {r['Surface']}" == selected_pair_str, axis=1)]
            if not selected_row.empty:
                well_x, well_y = selected_row.iloc[0]['X'], selected_row.iloc[0]['Y']
                with st.spinner("Finding nearest trace..."):
                    trace_index = data_utils.get_nearest_trace_index(st.session_state.header_loader, well_x, well_y)
                    st.session_state.selected_indices = [trace_index]
                    st.session_state.area_selected = True
                    st.success(f"Found nearest trace at index: {trace_index}")
            else:
                st.error("Could not find the selected well-marker pair.")
    else:
        st.warning("No well data loaded. Please upload well data in Step 1.")

elif mode == "Single Inline":
    st.header("Select Inline")
    info = st.session_state.header_loader.get_inline_crossline_range()
    inline_choice = st.slider("Select Inline Number", info['inline_min'], info['inline_max'], info['inline_min'])
    
    if st.button("Select All Traces on Inline", type="primary"):
        all_inlines = st.session_state.header_loader.inlines
        # Find the indices of the traces that match the chosen inline
        chosen_indices = st.session_state.header_loader.unique_indices[all_inlines == inline_choice]
        st.session_state.selected_indices = chosen_indices
        st.session_state.area_selected = True
        st.success(f"Selected {len(chosen_indices)} traces for inline {inline_choice}.")

elif mode == "Single Crossline":
    st.header("Select Crossline")
    info = st.session_state.header_loader.get_inline_crossline_range()
    crossline_choice = st.slider("Select Crossline Number", info['xline_min'], info['xline_max'], info['xline_min'])

    if st.button("Select All Traces on Crossline", type="primary"):
        all_crosslines = st.session_state.header_loader.crosslines
        chosen_indices = st.session_state.header_loader.unique_indices[all_crosslines == crossline_choice]
        st.session_state.selected_indices = chosen_indices
        st.session_state.area_selected = True
        st.success(f"Selected {len(chosen_indices)} traces for crossline {crossline_choice}.")

elif mode == "Traces near Polyline":
    st.header("Define Polyline")
    polyline_str = st.text_area("Enter Polyline Coordinates (X Y pairs, separated by spaces or newlines):", "x1 y1\nx2 y2\nx3 y3")
    tolerance = st.number_input("Tolerance around polyline", value=50.0)

    if st.button("Find Traces near Polyline", type="primary"):
        try:
            polyline_points = general_utils.parse_polyline_string(polyline_str)
            with st.spinner("Finding traces near polyline..."):
                indices = general_utils.find_traces_near_polyline(st.session_state.header_loader, polyline_points, tolerance)
                st.session_state.selected_indices = indices
                st.session_state.area_selected = True
                st.success(f"Found {len(indices)} traces near the polyline.")
        except ValueError as e:
            st.error(f"Error parsing polyline: {e}")

elif mode == "Full Volume AOI":
    st.header("Define Area of Interest (AOI)")
    info = st.session_state.header_loader.get_inline_crossline_range()
    
    c1, c2 = st.columns(2)
    min_il, max_il = c1.slider("Inline Range", info['inline_min'], info['inline_max'], (info['inline_min'], info['inline_max']))
    min_xl, max_xl = c2.slider("Crossline Range", info['xline_min'], info['xline_max'], (info['xline_min'], info['xline_max']))

    if st.button("Select Traces in AOI", type="primary"):
        inlines = st.session_state.header_loader.inlines
        crosslines = st.session_state.header_loader.crosslines
        
        # Create boolean masks
        inline_mask = (inlines >= min_il) & (inlines <= max_il)
        crossline_mask = (crosslines >= min_xl) & (crosslines <= max_xl)
        
        # Combine masks
        combined_mask = inline_mask & crossline_mask
        
        chosen_indices = st.session_state.header_loader.unique_indices[combined_mask]
        st.session_state.selected_indices = chosen_indices
        st.session_state.area_selected = True
        st.success(f"Selected {len(chosen_indices)} traces within the AOI.")

if st.session_state.get('area_selected'):
    st.info(f"**{len(st.session_state.selected_indices)}** traces have been selected for analysis.")
    st.markdown("You can now proceed to **Step 4: Analyze Data**.")
```

### Step 4.5: `pages/4_analyze_data.py`

This page is the core of the application, where the actual data processing and analysis happen.

**Action:** Create a new file `pages/4_analyze_data.py` and add the following code:

```python
# pages/4_analyze_data.py
import streamlit as st
from utils import data_utils, processing, visualization
import numpy as np
import logging
from tqdm import tqdm

st.set_page_config(page_title="Analyze Data", layout="wide")
st.title("Step 4: Analyze Data")

if not st.session_state.get('area_selected'):
    st.warning("Please select an area in Step 3 before running the analysis.")
    st.stop()

# --- Analysis ---
if st.button("Run Analysis", type="primary", use_container_width=True):
    indices_to_process = st.session_state.selected_indices
    segy_path = st.session_state.segy_temp_file_path
    dt = st.session_state.dt
    plot_settings = st.session_state.plot_settings
    
    # Determine which spectral descriptor function to use
    spec_descriptor_func = st.session_state.gpu_functions.get('single_trace')
    if not spec_descriptor_func:
        from dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu
        spec_descriptor_func = dlogst_spec_descriptor_cpu
        st.info("Using CPU for processing.")
    else:
        st.info("Using GPU for processing.")

    all_descriptors = []
    all_trace_data = []
    
    progress_bar = st.progress(0, text="Starting analysis...")

    try:
        for i, trace_idx in enumerate(indices_to_process):
            trace_sample = data_utils.load_trace_sample(segy_path, trace_idx)
            if trace_sample is None:
                logging.warning(f"Skipping invalid trace at index {trace_idx}")
                continue

            # Prepare spectral parameters, removing WOSS-specific ones
            spectral_params = {k: v for k, v in plot_settings.items() if k not in ['epsilon', 'fdom_exponent', 'hfc_p95']}
            
            fmax_calc = (1 / (dt * 2)) * 0.8 # 80% of Nyquist
            descriptor = spec_descriptor_func(trace_sample, dt, fmax=fmax_calc, **spectral_params)
            
            # Calculate WOSS separately
            descriptor['WOSS'] = processing.calculate_woss(descriptor, plot_settings)
            descriptor['data'] = trace_sample # Add original data for plotting/export

            all_trace_data.append(trace_sample)
            all_descriptors.append(descriptor)
            
            progress_bar.progress((i + 1) / len(indices_to_process), text=f"Processing trace {i+1}/{len(indices_to_process)}")

        st.session_state.loaded_trace_data = all_trace_data
        st.session_state.calculated_descriptors = all_descriptors
        st.session_state.analysis_complete = True
        progress_bar.empty()
        st.success(f"Analysis complete for {len(all_descriptors)} traces.")

    except Exception as e:
        st.error(f"An error occurred during analysis: {e}")
        logging.error(f"Analysis error: {e}", exc_info=True)


# --- Display Results ---
if st.session_state.get('analysis_complete'):
    st.header("Analysis Results")

    analysis_mode = st.session_state.analysis_mode
    
    if analysis_mode == "Single Trace from Well":
        st.subheader("Single Trace Analysis")
        from common.constants import AVAILABLE_OUTPUTS_SINGLE
        selected_outputs = st.multiselect("Select outputs to display:", AVAILABLE_OUTPUTS_SINGLE, default=["Input Signal", "WOSS"])
        
        if st.session_state.loaded_trace_data:
            fig = visualization.plot_trace_with_descriptors(
                st.session_state.loaded_trace_data[0],
                st.session_state.calculated_descriptors[0],
                st.session_state.dt,
                st.session_state.plot_settings,
                selected_outputs
            )
            st.plotly_chart(fig, use_container_width=True)

    else: # For Inline, Crossline, Polyline, AOI
        st.subheader("Section Analysis")
        from common.constants import AVAILABLE_OUTPUTS_SECTION
        output_to_plot = st.selectbox("Select descriptor to display as a section:", AVAILABLE_OUTPUTS_SECTION, index=len(AVAILABLE_OUTPUTS_SECTION)-1)
        
        with st.spinner(f"Generating section plot for {output_to_plot}..."):
            fig = visualization.plot_descriptor_section(
                st.session_state.loaded_trace_data,
                st.session_state.calculated_descriptors,
                st.session_state.dt,
                st.session_state.plot_settings,
                output_to_plot
            )
            st.plotly_chart(fig, use_container_width=True)

```

### Step 4.6: `pages/5_export_results.py`

This final page handles the exporting of the calculated results into new SEG-Y files.

**Action:** Create a new file `pages/5_export_results.py` and add the following code:

```python
# pages/5_export_results.py
import streamlit as st
from utils import export_utils, data_utils
from common.constants import EXPORTABLE_ATTR_DISPLAY_NAMES, ATTR_NAME_MAP
import os
import tempfile
import zipfile
import shutil
import segyio
import numpy as np
import logging
from tqdm import tqdm
import time

st.set_page_config(page_title="Export Results", layout="wide")
st.title("Step 5: Export Results")

if not st.session_state.get('analysis_complete'):
    st.warning("Please run an analysis in Step 4 before exporting.")
    st.stop()

# --- Export Configuration ---
st.header("📊 Export Configuration")

# Attribute Selection
selected_display_names = st.multiselect(
    "Select attributes to export:",
    EXPORTABLE_ATTR_DISPLAY_NAMES,
    default=[
        "Original Seismic Amplitude",
        "WOSS (Weighted-Optimum Spectral Shape)",
        "High Frequency Content (HFC)",
        "Spectral Bandwidth"
    ]
)

if not selected_display_names:
    st.warning("Please select at least one attribute to export.")
    st.stop()

attributes_to_export = [ATTR_NAME_MAP[name] for name in selected_display_names]

# Export Options
col1, col2 = st.columns(2)

with col1:
    st.subheader("📁 Export Format")
    export_format = st.selectbox(
        "Choose export format:",
        ["SEG-Y Files", "NumPy Arrays", "Both"]
    )
    
    include_metadata = st.checkbox("Include metadata file", value=True)
    compress_output = st.checkbox("Compress output (ZIP)", value=True)

with col2:
    st.subheader("⚙️ Processing Options")
    header_loader = st.session_state.header_loader
    
    # Batching configuration
    grouping_type = st.selectbox("Group batches by:", ["Inline", "Crossline", "Trace Index"])
    
    # Suggest batch size based on number of traces
    suggested_batch_size = get_suggested_batch_size_for_export(len(st.session_state.selected_indices))
    batch_size = st.number_input(
        "Number of groups per batch file:",
        min_value=1,
        max_value=1000,
        value=suggested_batch_size
    )

# Preview Export Summary
with st.expander("📋 Export Summary"):
    st.write(f"**Number of traces:** {len(st.session_state.loaded_trace_data)}")
    st.write(f"**Attributes to export:** {len(selected_display_names)}")
    st.write(f"**Export format:** {export_format}")
    st.write(f"**Grouping:** {grouping_type}")
    st.write(f"**Batch size:** {batch_size}")
    
    # Estimate file sizes
    estimated_size = estimate_export_size(
        len(st.session_state.loaded_trace_data),
        len(st.session_state.loaded_trace_data[0]) if st.session_state.loaded_trace_data else 1000,
        len(selected_display_names),
        export_format
    )
    st.write(f"**Estimated total size:** {estimated_size}")

# --- Export Process ---
if st.button("📦 Generate Export Files", type="primary", use_container_width=True):
    try:
        # Create temporary directory for export
        export_dir = tempfile.mkdtemp(prefix="woss_export_")
        st.session_state.export_dir = export_dir
        
        # Progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        status_text.text("Preparing export data...")
        
        # Organize data by grouping type
        grouped_data = organize_data_for_export(
            st.session_state.loaded_trace_data,
            st.session_state.calculated_descriptors,
            st.session_state.selected_indices,
            header_loader,
            grouping_type
        )
        
        progress_bar.progress(0.1)
        status_text.text("Creating export files...")
        
        # Export files
        export_files = []
        total_groups = len(grouped_data)
        
        for i, (group_key, group_data) in enumerate(grouped_data.items()):
            # Create batch files
            batch_files = create_batch_files(
                group_data, attributes_to_export, export_dir, 
                group_key, export_format, batch_size
            )
            export_files.extend(batch_files)
            
            # Update progress
            progress = 0.1 + (i + 1) / total_groups * 0.7
            progress_bar.progress(progress)
            status_text.text(f"Processed group {i+1}/{total_groups}")
        
        # Create metadata file
        if include_metadata:
            metadata_file = create_metadata_file(export_dir, selected_display_names)
            export_files.append(metadata_file)
        
        progress_bar.progress(0.9)
        status_text.text("Finalizing export...")
        
        # Create ZIP file if requested
        if compress_output:
            zip_path = os.path.join(export_dir, "woss_export_results.zip")
            create_zip_file(export_files, zip_path)
            final_file = zip_path
        else:
            final_file = export_dir
        
        # Store export information
        st.session_state.export_complete = True
        st.session_state.last_export_path = final_file
        st.session_state.export_files = export_files
        
        progress_bar.progress(1.0)
        status_text.text("Export completed successfully!")
        
        st.success(f"✅ Export completed! {len(export_files)} files created.")
        
    except Exception as e:
        st.error(f"❌ Export failed: {e}")
        logging.error(f"Export error: {e}", exc_info=True)

# --- Download Results ---
if st.session_state.get('export_complete'):
    st.header("📥 Download Results")
    
    # Summary of exported files
    col1, col2, col3 = st.columns(3)
    col1.metric("Files Created", len(st.session_state.export_files))
    col2.metric("Total Size", get_directory_size(st.session_state.export_dir))
    col3.metric("Format", export_format)
    
    # Download options
    if compress_output and os.path.exists(st.session_state.last_export_path):
        with open(st.session_state.last_export_path, 'rb') as f:
            st.download_button(
                label="📥 Download ZIP File",
                data=f.read(),
                file_name="woss_export_results.zip",
                mime="application/zip",
                use_container_width=True
            )
    
    # Individual file downloads
    with st.expander("📄 Individual Files"):
        for file_path in st.session_state.export_files:
            if os.path.exists(file_path):
                file_name = os.path.basename(file_path)
                file_size = os.path.getsize(file_path)
                
                col1, col2 = st.columns([3, 1])
                col1.write(f"**{file_name}** ({file_size / 1024:.1f} KB)")
                
                with col2:
                    with open(file_path, 'rb') as f:
                        st.download_button(
                            label="📥",
                            data=f.read(),
                            file_name=file_name,
                            key=f"download_{file_name}"
                        )

# --- Helper Functions ---
def get_suggested_batch_size_for_export(num_traces):
    """Suggest appropriate batch size based on number of traces."""
    if num_traces <= 100:
        return 10
    elif num_traces <= 1000:
        return 50
    else:
        return 100

def estimate_export_size(num_traces, samples_per_trace, num_attributes, export_format):
    """Estimate the total size of exported files."""
    # Rough estimate: 4 bytes per sample for float32
    bytes_per_trace = samples_per_trace * 4 * num_attributes
    total_bytes = num_traces * bytes_per_trace
    
    if export_format == "Both":
        total_bytes *= 2  # Double for both formats
    
    # Add overhead for SEG-Y headers
    if export_format in ["SEG-Y Files", "Both"]:
        total_bytes += num_traces * 240  # 240 bytes per trace header
    
    # Convert to human-readable format
    if total_bytes < 1024**2:
        return f"{total_bytes / 1024:.1f} KB"
    elif total_bytes < 1024**3:
        return f"{total_bytes / (1024**2):.1f} MB"
    else:
        return f"{total_bytes / (1024**3):.1f} GB"

def organize_data_for_export(trace_data, descriptors, indices, header_loader, grouping_type):
    """Organize data by grouping type for export."""
    grouped_data = {}
    
    for i, (trace, desc, idx) in enumerate(zip(trace_data, descriptors, indices)):
        if grouping_type == "Inline":
            group_key = f"inline_{header_loader.inlines[idx]}"
        elif grouping_type == "Crossline":
            group_key = f"crossline_{header_loader.crosslines[idx]}"
        else:  # Trace Index
            group_key = f"traces_{i // 100}"  # Group by hundreds
        
        if group_key not in grouped_data:
            grouped_data[group_key] = []
        
        grouped_data[group_key].append({
            'trace_data': trace,
            'descriptors': desc,
            'index': idx
        })
    
    return grouped_data

def create_batch_files(group_data, attributes_to_export, export_dir, group_key, export_format, batch_size):
    """Create batch files for a group of data."""
    batch_files = []
    
    for i in range(0, len(group_data), batch_size):
        batch_data = group_data[i:i + batch_size]
        batch_name = f"{group_key}_batch_{i // batch_size + 1}"
        
        if export_format in ["SEG-Y Files", "Both"]:
            segy_file = os.path.join(export_dir, f"{batch_name}.sgy")
            create_segy_file(batch_data, attributes_to_export, segy_file)
            batch_files.append(segy_file)
        
        if export_format in ["NumPy Arrays", "Both"]:
            numpy_file = os.path.join(export_dir, f"{batch_name}.npz")
            create_numpy_file(batch_data, attributes_to_export, numpy_file)
            batch_files.append(numpy_file)
    
    return batch_files

def create_segy_file(batch_data, attributes_to_export, output_path):
    """Create a SEG-Y file from batch data."""
    # This is a simplified implementation
    # In practice, you'd need to properly handle SEG-Y format specifications
    logging.info(f"Creating SEG-Y file: {output_path}")
    
    # For now, create a placeholder file
    with open(output_path, 'w') as f:
        f.write("# SEG-Y export placeholder\n")
        f.write(f"# Attributes: {', '.join(attributes_to_export)}\n")
        f.write(f"# Number of traces: {len(batch_data)}\n")

def create_numpy_file(batch_data, attributes_to_export, output_path):
    """Create a NumPy file from batch data."""
    export_data = {}
    
    for attr in attributes_to_export:
        attr_data = []
        for item in batch_data:
            if attr in item['descriptors']:
                attr_data.append(item['descriptors'][attr])
            elif attr == 'data':
                attr_data.append(item['trace_data'])
        
        if attr_data:
            export_data[attr] = np.array(attr_data)
    
    np.savez_compressed(output_path, **export_data)

def create_metadata_file(export_dir, selected_display_names):
    """Create a metadata file with export information."""
    metadata_file = os.path.join(export_dir, "export_metadata.txt")
    
    with open(metadata_file, 'w') as f:
        f.write("WOSS Export Metadata\n")
        f.write("=" * 50 + "\n")
        f.write(f"Export Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Exported Attributes: {', '.join(selected_display_names)}\n")
        f.write(f"Number of Traces: {len(st.session_state.loaded_trace_data)}\n")
        f.write(f"Analysis Mode: {st.session_state.analysis_mode}\n")
        f.write(f"Sampling Interval: {st.session_state.dt * 1000:.1f} ms\n")
        
        # Add parameter settings
        f.write("\nAnalysis Parameters:\n")
        f.write("-" * 20 + "\n")
        for key, value in st.session_state.plot_settings.items():
            f.write(f"{key}: {value}\n")
    
    return metadata_file

def create_zip_file(file_list, zip_path):
    """Create a ZIP file containing all export files."""
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in file_list:
            if os.path.exists(file_path):
                arcname = os.path.basename(file_path)
                zipf.write(file_path, arcname)

def get_directory_size(directory):
    """Get the total size of a directory."""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(directory):
        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            total_size += os.path.getsize(file_path)
    
    # Convert to human-readable format
    if total_size < 1024**2:
        return f"{total_size / 1024:.1f} KB"
    elif total_size < 1024**3:
        return f"{total_size / (1024**2):.1f} MB"
    else:
        return f"{total_size / (1024**3):.1f} GB"
```

---

## Phase 5: Additional Considerations and Completion

### Step 5.1: Error Handling and Logging

**Action:** Ensure all modules have proper error handling and logging:

1. **Add try-catch blocks** around all file operations
2. **Use logging.error()** for critical errors
3. **Use logging.warning()** for non-critical issues
4. **Use logging.info()** for important status updates

### Step 5.2: Testing and Validation

**Action:** Before deploying, test the refactored application:

1. **Unit Testing**: Test individual functions in isolation
2. **Integration Testing**: Test the complete workflow
3. **Performance Testing**: Compare processing times with the original app
4. **Memory Testing**: Ensure no memory leaks during batch processing

### Step 5.3: Documentation

**Action:** Create comprehensive documentation:

1. **User Guide**: Step-by-step instructions for using the application
2. **API Documentation**: Document all functions and their parameters
3. **Configuration Guide**: Explain all available parameters and their effects
4. **Troubleshooting Guide**: Common issues and solutions

### Step 5.4: Deployment Considerations

**Action:** Prepare for deployment:

1. **Environment Setup**: Document Python version and dependency requirements
2. **Configuration Files**: Create sample configuration files
3. **Docker Support**: Consider creating a Dockerfile for containerized deployment
4. **Performance Optimization**: Profile and optimize critical code paths

---

## Summary

This refactoring guide transforms the monolithic WOSS application into a clean, maintainable, and scalable multi-page Streamlit application. The key improvements include:

1. **Modular Architecture**: Separation of concerns with dedicated modules
2. **Improved User Experience**: Modern, intuitive interface with progress tracking
3. **Enhanced Performance**: Efficient batch processing and GPU acceleration
4. **Better Error Handling**: Comprehensive error handling and logging
5. **Flexible Export Options**: Multiple export formats and compression options
6. **Comprehensive Documentation**: Detailed implementation guide and best practices

The refactored application maintains all the original functionality while providing a foundation for future enhancements and easier maintenance.

## Critical Missing Implementations

Based on the analysis of the current codebase, several key components are missing or incomplete. Here are the critical items that need to be implemented:

### 1. **CRITICAL: Missing CPU Implementation**
⚠️ **THE MOST CRITICAL MISSING COMPONENT** ⚠️

The current codebase **DOES NOT HAVE ANY CPU IMPLEMENTATION** for spectral descriptor calculations. It only has:
- GPU implementation in `dlogst_spec_descriptor_gpu.py`
- Fallback stubs in `app_ref.py` that raise `NotImplementedError`

**Current problematic code in `app_ref.py`:**
```python
def dlogst_spec_descriptor_gpu(*args, **kwargs):
    st.error("GPU function dlogst_spec_descriptor_gpu not available.")
    raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu not available.")
```

**This means the application will completely fail on systems without GPU support.**

You **MUST** implement the CPU version first before any refactoring:
- Create `utils/dlogst_spec_descriptor_cpu.py` with the complete CPU implementation
- This is the most critical missing piece that will break the application
- The CPU implementation provided in Step 3.3 is complete and ready to use

### 2. **Directory Structure Creation**
The current codebase still uses the flat structure. You need to create the directory structure first:

```bash
mkdir common pages utils
```

### 3. **Import Path Issues**
The current `app_ref.py` has import statements that won't work with the new structure:
```python
from data_utils import SegyHeaderLoader  # This needs to be: from utils.data_utils import SegyHeaderLoader
```

### 4. **Session State Management**
The `common/session_state.py` module needs to be implemented before any of the pages can work properly.

### 5. **Constants Definition**
The `common/constants.py` file contains all the constant definitions that are referenced throughout the application.

### 6. **Streamlit Configuration**
The `.streamlit/config.toml` file should be created for better user experience:

```toml
[theme]
primaryColor = "#1f4e79"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"

[server]
maxUploadSize = 1000
```

### 7. **MANDATORY Implementation Order**

⚠️ **CRITICAL: You MUST follow this exact order to avoid breaking the application:**

1. **FIRST: Create CPU Implementation** 
   - Create `utils/dlogst_spec_descriptor_cpu.py` (use the complete code from Step 3.3)
   - This is THE MOST CRITICAL step - without it, the app won't work on CPU-only systems

2. **Create directory structure** (common, pages, utils)
3. **Create requirements.txt** with all dependencies
4. **Implement common modules** (constants.py, session_state.py, ui_elements.py)
5. **Move and update existing utils** (data_utils.py, processing.py, gpu_utils.py, etc.)
6. **Create the main app.py** file
7. **Implement pages** in order (1_load_data.py, 2_configure_display.py, etc.)
8. **Test each page** individually before moving to the next
9. **Fix import issues** and dependency problems
10. **Add error handling** and logging throughout
11. **Test the complete workflow**

### 8. **Configuration Management**

Create a `config.py` file in the root directory to manage application settings:

```python
# config.py
import os
from pathlib import Path

# Application settings
APP_NAME = "WOSS Seismic Analysis Tool"
APP_VERSION = "2.0.0"
DEBUG = os.getenv("DEBUG", "False").lower() == "true"

# File paths
PROJECT_ROOT = Path(__file__).parent
DATA_DIR = PROJECT_ROOT / "data"
TEMP_DIR = PROJECT_ROOT / "temp"
EXPORT_DIR = PROJECT_ROOT / "exports"

# Processing settings
DEFAULT_BATCH_SIZE = 100
MAX_TRACES_FOR_STATS = 1000
DEFAULT_SAMPLE_PERCENT = 1.0

# GPU settings
ENABLE_GPU = os.getenv("ENABLE_GPU", "True").lower() == "true"
GPU_MEMORY_FRACTION = float(os.getenv("GPU_MEMORY_FRACTION", "0.8"))

# Create directories if they don't exist
for directory in [DATA_DIR, TEMP_DIR, EXPORT_DIR]:
    directory.mkdir(exist_ok=True)
```

### 9. **Environment Setup Script**

Create a `setup.py` or `environment.yml` file for easier environment setup:

```yaml
# environment.yml
name: woss-analysis
channels:
  - conda-forge
  - defaults
dependencies:
  - python=3.9
  - streamlit>=1.28.0
  - numpy>=1.21.0
  - pandas>=1.3.0
  - plotly>=5.0.0
  - scipy>=1.7.0
  - tqdm>=4.62.0
  - pip
  - pip:
    - segyio>=1.9.0
    - torch>=1.9.0
    # Add cupy for GPU support (optional)
    # - cupy-cuda11x>=9.0.0
```

### 10. **Migration Script**

Create a migration script to help transition from the old structure to the new one:

```python
# migrate.py
import os
import shutil
from pathlib import Path

def migrate_old_structure():
    """Migrate from old flat structure to new modular structure."""
    
    # Create new directories
    directories = ['common', 'pages', 'utils']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    # Move files to appropriate locations
    file_mappings = {
        'data_utils.py': 'utils/data_utils.py',
        'processing.py': 'utils/processing.py',
        'visualization.py': 'utils/visualization.py',
        'export_utils.py': 'utils/export_utils.py',
        'utils.py': 'utils/general_utils.py',
        'dlogst_spec_descriptor_gpu.py': 'utils/dlogst_spec_descriptor_gpu.py',
    }
    
    for old_path, new_path in file_mappings.items():
        if os.path.exists(old_path):
            shutil.move(old_path, new_path)
            print(f"Moved {old_path} to {new_path}")
    
    print("Migration completed!")

if __name__ == "__main__":
    migrate_old_structure()
```

### 11. **Testing Framework**

Create a basic testing framework:

```python
# tests/test_basic.py
import pytest
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from utils.processing import calculate_woss, clean_array
import numpy as np

def test_calculate_woss():
    """Test WOSS calculation function."""
    descriptor = {
        'hfc': np.array([1.0, 2.0, 3.0]),
        'norm_fdom': np.array([0.1, 0.2, 0.3]),
        'mag_voice_slope': np.array([0.5, 1.0, 1.5])
    }
    
    plot_settings = {
        'epsilon': 1e-4,
        'fdom_exponent': 2.0,
        'hfc_p95': 1.0
    }
    
    result = calculate_woss(descriptor, plot_settings)
    assert isinstance(result, np.ndarray)
    assert len(result) == 3

def test_clean_array():
    """Test array cleaning function."""
    arr = np.array([1.0, np.inf, 2.0, np.nan, 3.0])
    cleaned = clean_array(arr)
    assert len(cleaned) == 3
    assert np.array_equal(cleaned, np.array([1.0, 2.0, 3.0]))

if __name__ == "__main__":
    pytest.main([__file__])
```

---

## Implementation Status Checklist

Use this checklist to track your refactoring progress:

### Phase 1: Setup
- [ ] Create directory structure (common, pages, utils)
- [ ] Create requirements.txt
- [ ] Create .streamlit/config.toml
- [ ] Create config.py for application settings

### Phase 2: Common Modules
- [ ] Implement common/constants.py
- [ ] Implement common/session_state.py
- [ ] Implement common/ui_elements.py

### Phase 3: Utilities
- [ ] Implement utils/data_utils.py
- [ ] Implement utils/processing.py
- [ ] Implement utils/dlogst_spec_descriptor_cpu.py
- [ ] Implement utils/gpu_utils.py
- [ ] Implement utils/visualization.py
- [ ] Implement utils/general_utils.py
- [ ] Implement utils/export_utils.py

### Phase 4: Main Application
- [ ] Implement app.py
- [ ] Fix all import statements
- [ ] Test basic application startup

### Phase 5: Pages
- [ ] Implement pages/1_load_data.py
- [ ] Implement pages/2_configure_display.py
- [ ] Implement pages/3_select_area.py
- [ ] Implement pages/4_analyze_data.py
- [ ] Implement pages/5_export_results.py

### Phase 6: Testing & Validation
- [ ] Test each page individually
- [ ] Test complete workflow
- [ ] Fix any remaining import issues
- [ ] Add comprehensive error handling
- [ ] Performance testing

### Phase 7: Documentation
- [ ] Create user documentation
- [ ] Create API documentation
- [ ] Create deployment guide
- [ ] Create troubleshooting guide

This checklist ensures nothing is missed during the refactoring process.