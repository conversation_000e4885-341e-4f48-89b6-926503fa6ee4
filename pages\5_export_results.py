# pages/5_export_results.py
import streamlit as st
import numpy as np
import pandas as pd
import tempfile
import os
import zipfile
import logging
import time
from utils.export_utils import (
    create_segy_export, 
    create_csv_export,
    create_excel_export,
    validate_export_data
)
from utils.gpu_utils import clear_gpu_memory
from common.session_state import initialize_session_state
from common.constants import EXPORTABLE_ATTRIBUTES

st.set_page_config(page_title="Export Results", layout="wide")
st.title("Step 5: Export Results (GPU-Optimized)")

# Initialize session state
initialize_session_state()

# Check prerequisites
if not st.session_state.get('analysis_complete'):
    st.error("❌ No analysis results available. Please complete Step 4 first.")
    st.stop()

if not st.session_state.get('analysis_results'):
    st.error("❌ Analysis results not found. Please run analysis in Step 4.")
    st.stop()

# Display export status
st.markdown("### 📤 Export Status")
if st.session_state.get('gpu_available'):
    st.success("🚀 GPU-accelerated export processing available")
else:
    st.info("💻 Using CPU export processing")

st.markdown("---")

# Get analysis results
results = st.session_state.analysis_results
analysis_mode = st.session_state.get('analysis_mode', 'Unknown')
processing_time = st.session_state.get('processing_time', 0)

# Export summary
st.markdown("### 📊 Export Summary")
col1, col2, col3 = st.columns(3)

with col1:
    st.write(f"**Analysis Mode:** {analysis_mode}")
    st.write(f"**Traces Processed:** {len(results['trace_data'])}")
    st.write(f"**Processing Time:** {processing_time:.1f}s")

with col2:
    st.write(f"**Backend Used:** {results['processing_config']['backend']}")
    st.write(f"**Descriptors Available:** {len(results['descriptors'])}")
    if 'processing_config' in results:
        st.write(f"**Batch Size Used:** {results['processing_config']['batch_size']}")

with col3:
    # Calculate data size estimation
    trace_count = len(results['trace_data'])
    descriptor_count = len(results['descriptors'])
    est_size_mb = (trace_count * 1000 * 4 + descriptor_count * 100 * 4) / (1024 * 1024)
    st.write(f"**Est. Export Size:** {est_size_mb:.1f} MB")
    st.write(f"**Available Attributes:** {len(EXPORTABLE_ATTRIBUTES)}")

st.markdown("---")

# Export configuration
st.markdown("### ⚙️ Export Configuration")

col1, col2 = st.columns(2)

with col1:
    st.markdown("#### 📋 Export Format")
    export_format = st.selectbox(
        "Select export format:",
        ["SEG-Y", "CSV", "Excel", "All Formats"],
        help="Choose the format for exporting analysis results"
    )
    
    include_original_data = st.checkbox(
        "Include original seismic data",
        value=True,
        help="Include original trace data in export"
    )
    
    compress_output = st.checkbox(
        "Compress output files",
        value=True,
        help="Create ZIP archive of export files"
    )

with col2:
    st.markdown("#### 🎯 Attribute Selection")
    
    # Get available attributes from results
    available_attrs = []
    if results['descriptors']:
        # Get attributes from first descriptor
        first_descriptor = results['descriptors'][0]
        if isinstance(first_descriptor, dict):
            available_attrs = list(first_descriptor.keys())
        else:
            available_attrs = list(EXPORTABLE_ATTRIBUTES.keys())
    
    if not available_attrs:
        available_attrs = list(EXPORTABLE_ATTRIBUTES.keys())
    
    selected_attributes = st.multiselect(
        "Select attributes to export:",
        available_attrs,
        default=available_attrs[:5] if len(available_attrs) > 5 else available_attrs,
        help="Choose which spectral attributes to include in export"
    )
    
    if include_original_data:
        selected_attributes = ['data'] + [attr for attr in selected_attributes if attr != 'data']

# Export file naming
st.markdown("#### 📁 File Naming")
col1, col2 = st.columns(2)

with col1:
    export_prefix = st.text_input(
        "Export file prefix:",
        value="woss_analysis",
        help="Prefix for exported files"
    )

with col2:
    include_timestamp = st.checkbox(
        "Include timestamp in filename",
        value=True,
        help="Add timestamp to avoid filename conflicts"
    )

st.markdown("---")

# Export execution
def execute_export():
    """Execute the export process with GPU optimization."""
    try:
        st.markdown("### 🚀 Export Execution")
        
        # Create progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # Create temporary directory for export
        export_dir = tempfile.mkdtemp(prefix="woss_export_")
        st.session_state.export_dir = export_dir
        
        start_time = time.time()
        export_files = []
        
        # Generate timestamp if needed
        timestamp = ""
        if include_timestamp:
            timestamp = f"_{int(time.time())}"
        
        # Validate export data
        status_text.text("Validating export data...")
        progress_bar.progress(0.1)
        
        if not validate_export_data(results, selected_attributes):
            st.error("❌ Export data validation failed")
            return None
        
        # Export based on format selection
        if export_format in ["SEG-Y", "All Formats"]:
            status_text.text("Creating SEG-Y export...")
            progress_bar.progress(0.3)
            
            segy_file = os.path.join(export_dir, f"{export_prefix}_segy{timestamp}.sgy")
            success = create_segy_export(
                results, selected_attributes, segy_file,
                st.session_state.header_loader, st.session_state.dt
            )
            if success:
                export_files.append(segy_file)
        
        if export_format in ["CSV", "All Formats"]:
            status_text.text("Creating CSV export...")
            progress_bar.progress(0.5)
            
            csv_file = os.path.join(export_dir, f"{export_prefix}_data{timestamp}.csv")
            success = create_csv_export(results, selected_attributes, csv_file)
            if success:
                export_files.append(csv_file)
        
        if export_format in ["Excel", "All Formats"]:
            status_text.text("Creating Excel export...")
            progress_bar.progress(0.7)
            
            excel_file = os.path.join(export_dir, f"{export_prefix}_data{timestamp}.xlsx")
            success = create_excel_export(results, selected_attributes, excel_file)
            if success:
                export_files.append(excel_file)
        
        # Create ZIP archive if requested
        if compress_output and export_files:
            status_text.text("Creating ZIP archive...")
            progress_bar.progress(0.9)
            
            zip_file = os.path.join(export_dir, f"{export_prefix}_export{timestamp}.zip")
            with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in export_files:
                    if os.path.exists(file_path):
                        zipf.write(file_path, os.path.basename(file_path))
            
            export_files = [zip_file]  # Only keep ZIP file for download
        
        progress_bar.progress(1.0)
        end_time = time.time()
        export_time = end_time - start_time
        
        status_text.success(f"✅ Export completed in {export_time:.1f} seconds")
        
        # Store export results
        st.session_state.export_complete = True
        st.session_state.export_files = export_files
        st.session_state.export_time = export_time
        st.session_state.export_format = export_format
        
        # Clear GPU memory
        clear_gpu_memory()
        
        return export_files
        
    except Exception as e:
        st.error(f"❌ Export failed: {str(e)}")
        logging.error(f"Export execution failed: {e}")
        clear_gpu_memory()
        return None

# Export execution button
if not st.session_state.get('export_complete'):
    if selected_attributes:
        if st.button("📤 Start Export Process", type="primary", use_container_width=True):
            with st.spinner("Executing export process..."):
                export_files = execute_export()
                if export_files:
                    st.rerun()
    else:
        st.warning("⚠️ Please select at least one attribute to export")

# Display export results and download options
if st.session_state.get('export_complete') and st.session_state.get('export_files'):
    st.markdown("---")
    st.markdown("### 📥 Download Results")
    
    export_files = st.session_state.export_files
    export_time = st.session_state.get('export_time', 0)
    
    # Export summary
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Export Time", f"{export_time:.1f}s")
    with col2:
        st.metric("Files Created", len(export_files))
    with col3:
        # Calculate total file size
        total_size = 0
        for file_path in export_files:
            if os.path.exists(file_path):
                total_size += os.path.getsize(file_path)
        total_size_mb = total_size / (1024 * 1024)
        st.metric("Total Size", f"{total_size_mb:.1f} MB")
    
    # Download buttons
    for file_path in export_files:
        if os.path.exists(file_path):
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path) / (1024 * 1024)
            
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # Determine MIME type
            if filename.endswith('.zip'):
                mime_type = "application/zip"
            elif filename.endswith('.csv'):
                mime_type = "text/csv"
            elif filename.endswith('.xlsx'):
                mime_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            elif filename.endswith('.sgy'):
                mime_type = "application/octet-stream"
            else:
                mime_type = "application/octet-stream"
            
            st.download_button(
                label=f"📥 Download {filename} ({file_size:.1f} MB)",
                data=file_data,
                file_name=filename,
                mime=mime_type,
                use_container_width=True
            )
    
    st.success("✅ Export process completed successfully!")

# Reset export button
if st.session_state.get('export_complete'):
    st.markdown("---")
    if st.button("🔄 Reset Export", help="Clear export results and configure new export"):
        # Clear export results
        for key in ['export_complete', 'export_files', 'export_time', 'export_format', 'export_dir']:
            if key in st.session_state:
                del st.session_state[key]
        clear_gpu_memory()
        st.rerun()

# Navigation
st.markdown("---")
col1, col2 = st.columns(2)

with col1:
    if st.button("⬅️ Back to Analysis", use_container_width=True):
        st.switch_page("pages/4_analyze_data.py")

with col2:
    if st.button("🏠 Back to Home", use_container_width=True):
        st.switch_page("app.py")
