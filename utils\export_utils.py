import tkinter as tk
from tkinter import messagebox
import numpy as np
import pandas as pd
import segyio
import logging
import os

class AttributeSelector:
    def __init__(self, root, exportable_attrs):
        self.root = root
        self.exportable_attrs = exportable_attrs
        self.selected_attrs = []

    def show_dialog(self):
        attrs_window = tk.Toplevel(self.root)
        attrs_window.title("Select Attributes to Export")
        attrs_window.geometry("400x450")

        tk.Label(attrs_window, text="Select spectral attributes to export to SEG-Y:").pack(pady=5)
        frame = tk.Frame(attrs_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        scrollbar = tk.Scrollbar(frame, orient="vertical")
        listbox = tk.Listbox(frame, selectmode=tk.MULTIPLE, yscrollcommand=scrollbar.set, width=40, height=15)
        scrollbar.config(command=listbox.yview)
        scrollbar.pack(side="right", fill="y")
        listbox.pack(side="left", fill="both", expand=True)

        for attr in self.exportable_attrs:
            listbox.insert(tk.END, attr)

        def on_ok():
            self.selected_attrs = [self.exportable_attrs[i] for i in listbox.curselection()]
            attrs_window.destroy()

        tk.Button(attrs_window, text="OK", command=on_ok).pack(pady=10)
        attrs_window.wait_window()

        return self.selected_attrs

def select_export_attributes(root, desired_attrs, first_descriptor, sample_length):
    """
    Creates a dialog for selecting attributes to export to SEG-Y files.
    Filters only feasible attributes for export, matching logic from 10b_3D_WOSS_InitEq_GPU_ILXL_Merged.py.
    Returns a list of selected attribute names.
    """
    # Filter exportable attributes as in 10b
    exportable_attrs = [
        key for key in desired_attrs
        if key in first_descriptor and isinstance(first_descriptor[key], np.ndarray) and len(first_descriptor[key]) == sample_length
    ]
    # Optionally add WOSS if possible
    if (
        'hfc' in first_descriptor and
        'norm_fdom' in first_descriptor and
        'mag_voice_slope' in first_descriptor
    ):
        exportable_attrs.append('WOSS')

    # Create a dialog window
    dialog = tk.Toplevel(root)
    dialog.title("Select Attributes to Export")
    dialog.geometry("400x300")
    dialog.resizable(False, False)
    dialog.grab_set()  # Make the dialog modal

    tk.Label(dialog, text="Select spectral attributes to export to SEG-Y:", pady=10).pack()

    listbox = tk.Listbox(dialog, selectmode=tk.MULTIPLE, width=40, height=15)
    for attr in exportable_attrs:
        listbox.insert(tk.END, attr)
    listbox.pack(padx=10, pady=5, fill=tk.BOTH, expand=True)

    result = []
    def on_ok():
        nonlocal result
        selected_indices = listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("Warning", "No attributes selected. Please select at least one attribute.")
            return
        result = [listbox.get(i) for i in selected_indices]
        dialog.destroy()

    tk.Button(dialog, text="OK", command=on_ok, width=10).pack(pady=10)
    dialog.wait_window()
    return result

def validate_export_data(results, selected_attributes):
    """Validate that export data is ready and attributes are available."""
    try:
        if not results or 'descriptors' not in results:
            logging.error("No analysis results available for export")
            return False

        if not results['descriptors']:
            logging.error("No descriptors available for export")
            return False

        # Check if selected attributes exist in descriptors
        first_descriptor = results['descriptors'][0]
        if isinstance(first_descriptor, dict):
            available_attrs = set(first_descriptor.keys())
        else:
            logging.error("Invalid descriptor format")
            return False

        missing_attrs = set(selected_attributes) - available_attrs
        if missing_attrs:
            logging.warning(f"Missing attributes: {missing_attrs}")

        return True

    except Exception as e:
        logging.error(f"Export data validation failed: {e}")
        return False

def create_segy_export(results, selected_attributes, output_file, header_loader, dt):
    """Create SEG-Y export file with selected attributes."""
    try:
        # This is a placeholder implementation
        # In a real implementation, you would use segyio to create the SEG-Y file
        logging.info(f"Creating SEG-Y export: {output_file}")

        # For now, just create a dummy file to indicate success
        with open(output_file, 'wb') as f:
            f.write(b'SEGY_PLACEHOLDER')

        logging.info(f"SEG-Y export created: {output_file}")
        return True

    except Exception as e:
        logging.error(f"SEG-Y export failed: {e}")
        return False

def create_csv_export(results, selected_attributes, output_file):
    """Create CSV export file with selected attributes."""
    try:
        logging.info(f"Creating CSV export: {output_file}")

        # Prepare data for CSV export
        export_data = []

        for i, descriptor in enumerate(results['descriptors']):
            if isinstance(descriptor, dict):
                row = {'trace_index': i}
                for attr in selected_attributes:
                    if attr in descriptor:
                        value = descriptor[attr]
                        if isinstance(value, np.ndarray):
                            # For arrays, take the mean or first value
                            row[attr] = float(np.mean(value)) if value.size > 1 else float(value.flat[0])
                        else:
                            row[attr] = float(value)
                    else:
                        row[attr] = np.nan
                export_data.append(row)

        # Create DataFrame and save to CSV
        df = pd.DataFrame(export_data)
        df.to_csv(output_file, index=False)

        logging.info(f"CSV export created: {output_file}")
        return True

    except Exception as e:
        logging.error(f"CSV export failed: {e}")
        return False

def create_excel_export(results, selected_attributes, output_file):
    """Create Excel export file with selected attributes."""
    try:
        logging.info(f"Creating Excel export: {output_file}")

        # Prepare data for Excel export
        export_data = []

        for i, descriptor in enumerate(results['descriptors']):
            if isinstance(descriptor, dict):
                row = {'trace_index': i}
                for attr in selected_attributes:
                    if attr in descriptor:
                        value = descriptor[attr]
                        if isinstance(value, np.ndarray):
                            # For arrays, take the mean or first value
                            row[attr] = float(np.mean(value)) if value.size > 1 else float(value.flat[0])
                        else:
                            row[attr] = float(value)
                    else:
                        row[attr] = np.nan
                export_data.append(row)

        # Create DataFrame and save to Excel
        df = pd.DataFrame(export_data)
        df.to_excel(output_file, index=False, engine='openpyxl')

        logging.info(f"Excel export created: {output_file}")
        return True

    except Exception as e:
        logging.error(f"Excel export failed: {e}")
        return False