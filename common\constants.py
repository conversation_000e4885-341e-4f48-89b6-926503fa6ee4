# common/constants.py
"""
GPU-optimized constants and configuration for WOSS Seismic Analysis Tool.

This module contains all application constants optimized for GPU processing
with appropriate fallback values for CPU processing.
"""

# Application Configuration
APP_TITLE = "WOSS Seismic Analysis Tool (GPU-Accelerated)"
VERSION = "2.0.0-GPU"

# GPU Processing Configuration
GPU_PROCESSING_PREFERRED = True
GPU_MEMORY_SAFETY_FACTOR = 0.8  # Use 80% of available GPU memory

# GPU-Optimized Batch Sizes for Different Analysis Modes
GPU_BATCH_SIZES = {
    "Single inline (all crosslines)": 1024,
    "Single crossline (all inlines)": 1024, 
    "By inline/crossline section (AOI)": 512,
    "By Polyline File Import": 256,
    "By well markers": 128
}

# CPU Fallback Batch Sizes (smaller for memory efficiency)
CPU_FALLBACK_BATCH_SIZES = {
    "Single inline (all crosslines)": 64,
    "Single crossline (all inlines)": 64,
    "By inline/crossline section (AOI)": 32, 
    "By Polyline File Import": 16,
    "By well markers": 8
}

# Analysis Modes (GPU-optimized)
ANALYSIS_MODES = [
    "Single inline (all crosslines)",    # GPU-optimized for line processing
    "Single crossline (all inlines)",    # GPU-optimized for line processing  
    "By inline/crossline section (AOI)", # GPU-optimized for area processing
    "By Polyline File Import",           # GPU-optimized for polyline processing
    "By well markers"                    # GPU-optimized for point processing
]

# GPU-Optimized Output Types (All Modes)
AVAILABLE_OUTPUTS_ALL_MODES = [
    "Input Signal", "HFC", "Spectral Decrease", "Spectral Slope",
    "Mag*Voice Slope", "Voice Slope", "Peak Frequency", 
    "Spectral Centroid", "Dominant Frequency", "Spectral Bandwidth",
    "Spectral Rolloff", "Magnitude Spectrogram", "Magnitude * Voice",
    "WOSS", "Normalized Dominant Frequency"
]

# Outputs for section/polyline modes (excluding spectral images for performance)
AVAILABLE_OUTPUTS_SECTION = [
    "Input Signal",
    "Normalized dominant frequencies",
    "Spectral Slope", "Spectral Bandwidth",
    "Spectral Rolloff", "Mag*Voice Slope", "Spectral Decrease", "HFC", "WOSS"
]

# Export attributes optimized for GPU processing
EXPORTABLE_ATTRIBUTES = {
    "data": "Original Seismic Amplitude",
    "hfc": "High Frequency Content", 
    "spec_decrease": "Spectral Decrease",
    "spec_slope": "Spectral Slope",
    "mag_voice_slope": "Magnitude*Voice Slope",
    "spec_bandwidth": "Spectral Bandwidth", 
    "spec_rolloff": "Spectral Rolloff",
    "WOSS": "WOSS (Weighted-Optimum Spectral Shape)",
    "norm_fdom": "Normalized Dominant Frequency"
}

# Internal names used in descriptor dicts and for export keys
EXPORTABLE_ATTR_INTERNAL_NAMES = [
    "data", # Original seismic data
    "mag_voice_slope",
    "spec_decrease",
    "hfc",
    "spec_bandwidth",
    "spec_rolloff",
    "WOSS", # Calculated attribute
    "norm_fdom" # Note: This is frequency domain, export might need care
]

# GPU Memory Management Thresholds
GPU_MEMORY_THRESHOLDS = {
    "conservative": 0.6,  # Use 60% of GPU memory
    "aggressive": 0.8,    # Use 80% of GPU memory  
    "maximum": 0.9        # Use 90% of GPU memory
}

# Default GPU Memory Management Mode
DEFAULT_GPU_MEMORY_MODE = "aggressive"

# Descriptor Colormap/Amplitude Limits
DESCRIPTOR_LIMITS = {
    "Input Signal": {"min": None, "max": None},
    "HFC": {"min": None, "max": None},
    "Spectral Decrease": {"min": None, "max": None},
    "Spectral Slope": {"min": None, "max": None},
    "Mag*Voice Slope": {"min": None, "max": None},
    "Spectral Bandwidth": {"min": None, "max": None},
    "Spectral Rolloff": {"min": None, "max": None},
    "Magnitude Spectrogram": {"min": None, "max": None},
    "Magnitude * Voice": {"min": None, "max": None},
    "Normalized Dominant Frequency": {"min": None, "max": None},
    "WOSS": {"min": None, "max": None},
}

# Processing Configuration
DEFAULT_SAMPLING_STATS_PERCENT = 5.0  # Percentage of traces for statistics
MAX_TRACES_FOR_STATS = 1000  # Maximum traces for statistics calculation
DEFAULT_BATCH_SIZE = 512  # Default batch size when mode-specific not available

# GPU Processing Preferences
GPU_PROCESSING_MODES = {
    "auto": "Automatic GPU/CPU selection",
    "gpu_preferred": "Prefer GPU with CPU fallback", 
    "gpu_only": "GPU only (fail if unavailable)",
    "cpu_only": "CPU only processing"
}

DEFAULT_PROCESSING_MODE = "gpu_preferred"

# File Processing Limits
MAX_FILE_SIZE_MB = 10000  # Maximum file size in MB
MAX_TRACES_PER_BATCH = 2048  # Maximum traces per batch
MIN_TRACES_PER_BATCH = 1  # Minimum traces per batch

# UI Configuration
STREAMLIT_CONFIG = {
    "page_title": APP_TITLE,
    "layout": "wide",
    "initial_sidebar_state": "expanded"
}

# Logging Configuration
LOGGING_LEVEL = "INFO"
LOGGING_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
