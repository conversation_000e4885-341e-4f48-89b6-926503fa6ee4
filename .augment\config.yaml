# Augment Configuration File
# Configure codebase indexing and search behavior

# Exclude directories and files from indexing
exclude:
  # Archived scripts and files (not part of main pipeline)
  - "archive/"
  - "archive/**"
  
  # Documentation and development summaries  
  - "docs/"
  - "docs/**"
  
  # Python cache and compiled files
  - "__pycache__/"
  - "__pycache__/**"
  - "*.pyc"
  - "*.pyo"
  - "*.pyd"
  
  # Log files
  - "*.log"
  
  # IDE and editor files
  - ".vscode/"
  - ".idea/"
  - "*.swp"
  - "*.swo"
  - "*~"
  
  # OS generated files
  - ".DS_Store"
  - "Thumbs.db"
  
  # Temporary files
  - "*.tmp"
  - "*.temp"

# Include only main pipeline files
include:
  - "app.py"
  - "pages/"
  - "pages/**"
  - "common/"
  - "common/**"
  - "utils/"
  - "utils/**"
  - "requirements.txt"
  - "README.md"

# Indexing preferences
indexing:
  focus_directories:
    - "pages"
    - "common" 
    - "utils"
  
  priority_files:
    - "app.py"
    - "pages/*.py"
    - "common/*.py"
    - "utils/*.py"
