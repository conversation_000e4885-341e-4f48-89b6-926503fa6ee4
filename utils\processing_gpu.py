# utils/processing_gpu.py
import numpy as np
import logging
from tqdm import tqdm
from .gpu_utils import get_processing_functions, get_optimal_processing_config, clear_gpu_memory
from .data_utils import load_trace_sample

def process_inline_analysis_gpu(segy_path, inline_number, header_loader, dt, plot_settings):
    """GPU-optimized processing for single inline analysis."""
    logging.info(f"Starting GPU-optimized inline {inline_number} analysis")

    # Filter traces for the selected inline
    inline_mask = header_loader.inlines == inline_number
    trace_indices = header_loader.unique_indices[inline_mask]
    crosslines = header_loader.crosslines[inline_mask]

    # Get optimal processing configuration
    config = get_optimal_processing_config("Single inline (all crosslines)", len(trace_indices))

    # Process with GPU prioritization
    trace_data, descriptors = process_traces_gpu_batch(
        segy_path, trace_indices, dt, plot_settings, config
    )

    return {
        'trace_data': trace_data,
        'descriptors': descriptors,
        'crosslines': crosslines,
        'processing_config': config
    }

def process_crossline_analysis_gpu(segy_path, crossline_number, header_loader, dt, plot_settings):
    """GPU-optimized processing for single crossline analysis."""
    logging.info(f"Starting GPU-optimized crossline {crossline_number} analysis")

    # Filter traces for the selected crossline
    crossline_mask = header_loader.crosslines == crossline_number
    trace_indices = header_loader.unique_indices[crossline_mask]
    inlines = header_loader.inlines[crossline_mask]

    # Get optimal processing configuration
    config = get_optimal_processing_config("Single crossline (all inlines)", len(trace_indices))

    # Process with GPU prioritization
    trace_data, descriptors = process_traces_gpu_batch(
        segy_path, trace_indices, dt, plot_settings, config
    )

    return {
        'trace_data': trace_data,
        'descriptors': descriptors,
        'inlines': inlines,
        'processing_config': config
    }

def process_aoi_analysis_gpu(segy_path, inline_range, crossline_range, header_loader, dt, plot_settings):
    """GPU-optimized processing for Area of Interest (AOI) analysis."""
    logging.info(f"Starting GPU-optimized AOI analysis: IL{inline_range[0]}-{inline_range[1]}, XL{crossline_range[0]}-{crossline_range[1]}")

    # Filter traces within AOI bounds
    inline_mask = (header_loader.inlines >= inline_range[0]) & (header_loader.inlines <= inline_range[1])
    crossline_mask = (header_loader.crosslines >= crossline_range[0]) & (header_loader.crosslines <= crossline_range[1])
    aoi_mask = inline_mask & crossline_mask

    trace_indices = header_loader.unique_indices[aoi_mask]
    inlines = header_loader.inlines[aoi_mask]
    crosslines = header_loader.crosslines[aoi_mask]

    # Get optimal processing configuration for AOI
    config = get_optimal_processing_config("By inline/crossline section (AOI)", len(trace_indices))

    # Process with GPU prioritization
    trace_data, descriptors = process_traces_gpu_batch(
        segy_path, trace_indices, dt, plot_settings, config
    )

    return {
        'trace_data': trace_data,
        'descriptors': descriptors,
        'inlines': inlines,
        'crosslines': crosslines,
        'aoi_bounds': {'inline_range': inline_range, 'crossline_range': crossline_range},
        'processing_config': config
    }

def process_polyline_analysis_gpu(segy_path, polyline_indices, header_loader, dt, plot_settings):
    """GPU-optimized processing for polyline analysis."""
    logging.info(f"Starting GPU-optimized polyline analysis with {len(polyline_indices)} traces")

    # Get coordinates for polyline traces
    inlines = header_loader.inlines[polyline_indices]
    crosslines = header_loader.crosslines[polyline_indices]
    x_coords = header_loader.x_coords[polyline_indices]
    y_coords = header_loader.y_coords[polyline_indices]

    # Get optimal processing configuration for polyline
    config = get_optimal_processing_config("By Polyline File Import", len(polyline_indices))

    # Process with GPU prioritization
    trace_data, descriptors = process_traces_gpu_batch(
        segy_path, header_loader.unique_indices[polyline_indices], dt, plot_settings, config
    )

    return {
        'trace_data': trace_data,
        'descriptors': descriptors,
        'inlines': inlines,
        'crosslines': crosslines,
        'x_coords': x_coords,
        'y_coords': y_coords,
        'processing_config': config
    }

def process_traces_gpu_batch(segy_path, trace_indices, dt, plot_settings, config):
    """Core GPU batch processing function for all analysis modes."""
    processing_funcs = get_processing_functions()
    backend = processing_funcs['backend']
    batch_size = config['batch_size']

    logging.info(f"Processing {len(trace_indices)} traces using {backend} backend (batch size: {batch_size})")

    if backend == 'GPU' and processing_funcs['batch'] is not None:
        return _process_gpu_batched(segy_path, trace_indices, dt, plot_settings, processing_funcs, batch_size)
    else:
        return _process_cpu_sequential(segy_path, trace_indices, dt, plot_settings, processing_funcs, batch_size)

def _process_gpu_batched(segy_path, trace_indices, dt, plot_settings, processing_funcs, batch_size):
    """GPU-optimized batch processing using CuPy."""
    import cupy as cp

    all_trace_data = []
    all_descriptors = []

    # Process in GPU-optimized batches
    for i in tqdm(range(0, len(trace_indices), batch_size), desc="GPU Batch Processing"):
        batch_indices = trace_indices[i:i + batch_size]

        try:
            # Load batch traces
            batch_traces = []
            for idx in batch_indices:
                trace = load_trace_sample(segy_path, idx)
                batch_traces.append(trace)

            # Convert to GPU arrays for batch processing
            gpu_traces = cp.array(batch_traces, dtype=cp.float32)

            # Process entire batch on GPU
            batch_descriptors = processing_funcs['batch'](gpu_traces, dt, **plot_settings)

            # Convert results back to CPU
            cpu_traces = [cp.asnumpy(trace) for trace in gpu_traces]
            cpu_descriptors = {}
            for key, value in batch_descriptors.items():
                if hasattr(value, 'get'):  # CuPy array
                    cpu_descriptors[key] = cp.asnumpy(value)
                else:
                    cpu_descriptors[key] = value

            all_trace_data.extend(cpu_traces)
            all_descriptors.append(cpu_descriptors)

            # Clear GPU memory
            del gpu_traces, batch_descriptors

        except Exception as e:
            logging.error(f"GPU batch processing failed: {e}")
            # Fallback to CPU for this batch
            for idx in batch_indices:
                trace = load_trace_sample(segy_path, idx)
                descriptors = processing_funcs['single'](trace, dt, **plot_settings)
                all_trace_data.append(trace)
                all_descriptors.append(descriptors)

        # Clear GPU memory after each batch
        clear_gpu_memory()

    return all_trace_data, all_descriptors

def _process_cpu_sequential(segy_path, trace_indices, dt, plot_settings, processing_funcs, batch_size):
    """CPU fallback processing."""
    all_trace_data = []
    all_descriptors = []

    # Use smaller batches for CPU to manage memory
    cpu_batch_size = min(batch_size // 4, 32)

    for i in tqdm(range(0, len(trace_indices), cpu_batch_size), desc="CPU Processing"):
        batch_indices = trace_indices[i:i + cpu_batch_size]

        for idx in batch_indices:
            trace = load_trace_sample(segy_path, idx)
            descriptors = processing_funcs['single'](trace, dt, **plot_settings)

            all_trace_data.append(trace)
            all_descriptors.append(descriptors)

    return all_trace_data, all_descriptors


