# pages/__init__.py
"""
GPU-optimized page modules for WOSS Seismic Analysis Tool.

This package contains modular Streamlit page components with GPU optimization
for data loading, configuration, analysis mode selection, execution, and export.
"""

# Import page modules when they are created
try:
    from . import load_data
    from . import configure_display
    from . import select_area
    from . import analyze_data
    from . import export_results
except ImportError:
    # Page modules will be created in subsequent steps
    pass

__all__ = [
    'load_data',
    'configure_display',
    'select_area', 
    'analyze_data',
    'export_results'
]
