# Codebase Indexing Configuration

This document explains how the codebase indexing has been configured to exclude archived and documentation files from search results.

## Configuration Files Created

### 1. `.gitignore`
Standard Git ignore file that excludes:
- `archive/` directory and all contents
- `docs/` directory and all contents  
- Python cache files (`__pycache__/`, `*.pyc`)
- Log files (`*.log`)
- IDE files (`.vscode/`, `.idea/`)

### 2. `.augmentignore`
Augment-specific ignore file that mirrors `.gitignore` patterns for codebase indexing.

### 3. `.ignore`
Generic ignore file respected by many tools and indexing systems.

### 4. `.augment/config.yaml`
Comprehensive Augment configuration with explicit include/exclude rules.

## Excluded Directories

### `archive/` Directory
Contains scripts unrelated to the main pipeline:
- `app_ref.py` - Reference/backup application version
- `test_*.py` - Test scripts for development
- `woss_analysis.log` - Application log files

**Reason for Exclusion:** These files are not part of the active development pipeline and would create noise in codebase searches.

### `docs/` Directory  
Contains development documentation and summaries:
- `summary_step_*.md` - Development process summaries
- `next_phase_*.md` - Phase transition documentation
- `guideline.md`, `rules.md` - Project guidelines
- `refactoring_guideline_*.md` - Refactoring documentation

**Reason for Exclusion:** These are process documentation files, not code files that should appear in codebase retrieval.

## Included Files (Main Pipeline)

The following remain searchable and indexed:

### Core Application
- `app.py` - Main application entry point
- `requirements.txt` - Dependencies
- `README.md` - Project documentation

### Main Modules
- `pages/` - 5-step workflow pages
  - `1_load_data.py`
  - `2_configure_display.py` 
  - `3_select_area.py`
  - `4_analyze_data.py`
  - `5_export_results.py`

- `common/` - Shared resources
  - `constants.py`
  - `session_state.py`

- `utils/` - Core utilities
  - `data_utils.py`
  - `gpu_utils.py`
  - `processing.py`
  - `processing_gpu.py`
  - `visualization.py`
  - `export_utils.py`
  - `general_utils.py`
  - `dlogst_spec_descriptor_*.py`

## How to Apply Configuration

### For Augment Users:
1. The configuration files have been created in your workspace
2. Augment should automatically respect these ignore patterns
3. If indexing continues to include excluded files, contact Augment support to:
   - Force a re-index of the codebase
   - Verify that `.augmentignore` and `.augment/config.yaml` are being respected
   - Configure project-specific indexing rules

### Manual Configuration Steps:
If automatic configuration doesn't work, you may need to:

1. **Check Augment Settings:**
   - Look for indexing preferences in Augment's UI/settings
   - Verify that ignore files are being processed
   - Check if there's a manual re-index option

2. **Contact Support:**
   - Request that `archive/` and `docs/` be excluded from indexing
   - Ask for confirmation that ignore files are working
   - Request a fresh index rebuild

3. **Verify Configuration:**
   - Test codebase retrieval to ensure archived content doesn't appear
   - Confirm that only main pipeline files are returned in searches

## Testing the Configuration

To verify the configuration is working:

```
Search Query: "Find all test files"
Expected Result: No results from archive/ directory

Search Query: "Find processing functions" 
Expected Result: Only utils/processing.py and utils/processing_gpu.py

Search Query: "Find documentation files"
Expected Result: Only README.md, not docs/ content
```

## Benefits

1. **Focused Search Results:** Only active development files appear in searches
2. **Reduced Noise:** Archived and documentation files don't clutter results
3. **Better Context:** Codebase retrieval focuses on the actual pipeline
4. **Cleaner Development:** Clear separation between active and archived code

## Maintenance

- When adding new files to `archive/` or `docs/`, they will automatically be excluded
- The ignore patterns use wildcards (`**`) to catch all subdirectories
- Configuration files should be committed to version control to maintain consistency
