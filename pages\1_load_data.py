# pages/1_load_data.py
import streamlit as st
from utils.data_utils import <PERSON>gyHeader<PERSON>oader, load_excel_data, get_sampling_interval, get_trace_count
from utils.gpu_utils import initialize_gpu_system, GPU_AVAILABLE
from common.session_state import initialize_session_state
import tempfile
import os
import logging

st.set_page_config(page_title="Load Data", layout="wide")
st.title("Step 1: Load Data (GPU-Optimized)")

# Initialize session state and GPU system
initialize_session_state()

# Initialize GPU system if not already done
if not st.session_state.get('gpu_initialized'):
    with st.spinner("Initializing GPU system..."):
        gpu_available, backend, device_info = initialize_gpu_system()
        st.session_state.gpu_available = gpu_available
        st.session_state.gpu_backend = backend
        st.session_state.gpu_device_info = device_info
        st.session_state.gpu_initialized = True

# Display GPU status
st.markdown("### 🚀 Processing Backend Status")
if st.session_state.get('gpu_available'):
    col1, col2, col3 = st.columns(3)
    with col1:
        st.success("✅ GPU Acceleration Available")
    with col2:
        st.info(f"Backend: {st.session_state.get('gpu_backend', 'Unknown')}")
    with col3:
        if st.session_state.get('gpu_device_info'):
            st.info(f"Device: {st.session_state.gpu_device_info}")
else:
    st.warning("⚠️ GPU not available - using CPU processing")
    st.info(f"Backend: {st.session_state.get('gpu_backend', 'CPU Fallback')}")

st.markdown("---")

# --- Functions ---
@st.cache_data
def load_segy_headers_cached(segy_file_path, inline_byte, xline_byte, x_byte, y_byte, scaler_byte, use_custom_scaler):
    """Cached function to load SEG-Y headers with GPU memory considerations."""
    header_loader = SegyHeaderLoader(segy_file_path)
    header_loader.load_headers(inline_byte, xline_byte, x_byte, y_byte, scaler_byte, use_custom_scaler)
    return header_loader

@st.cache_data
def load_excel_data_cached(uploaded_file):
    """Cached function to load well data from Excel."""
    return load_excel_data(uploaded_file)

def estimate_gpu_memory_usage(trace_count, samples_per_trace=1000):
    """Estimate GPU memory usage for the dataset."""
    if not st.session_state.get('gpu_available'):
        return 0
    
    # Rough estimation: trace_count * samples * 4 bytes (float32) * processing overhead
    base_memory = trace_count * samples_per_trace * 4
    processing_overhead = 2.5  # Factor for processing overhead
    estimated_gb = (base_memory * processing_overhead) / (1024**3)
    return estimated_gb

# --- UI ---
st.sidebar.header("📁 Data Input")
segy_file = st.sidebar.file_uploader(
    "Upload SEG-Y File", 
    type=['sgy', 'segy'],
    help="Upload your seismic data file for GPU-accelerated analysis"
)
well_file = st.sidebar.file_uploader(
    "Upload Well Data (Optional Excel)", 
    type=['xlsx'],
    help="Optional well data for marker-based analysis"
)

st.sidebar.header("⚙️ SEG-Y Header Configuration")
st.sidebar.markdown("*Configure byte positions for header information*")

c1, c2 = st.sidebar.columns(2)
inline_byte = c1.number_input("Inline Byte", value=189, help="Byte position for inline numbers")
xline_byte = c2.number_input("Crossline Byte", value=193, help="Byte position for crossline numbers")
x_byte = c1.number_input("X-Coord Byte", value=73, help="Byte position for X coordinates")
y_byte = c2.number_input("Y-Coord Byte", value=77, help="Byte position for Y coordinates")

scaler_mode = st.sidebar.radio(
    "Coordinate Scaler", 
    ["Use Scaler Byte", "Use Custom Scaler"], 
    index=0,
    help="Choose how to scale coordinate values"
)

if scaler_mode == "Use Scaler Byte":
    scaler_byte = st.sidebar.number_input("Scaler Byte", value=71, help="Byte position for coordinate scaler")
    use_custom_scaler = False
else:
    scaler_byte = st.sidebar.number_input("Custom Scaler Value", value=1.0, help="Custom scaling factor")
    use_custom_scaler = True

# GPU Memory Management Options
if st.session_state.get('gpu_available'):
    st.sidebar.header("🚀 GPU Memory Management")
    memory_mode = st.sidebar.selectbox(
        "Memory Usage Mode:",
        ["Conservative (60%)", "Balanced (80%)", "Aggressive (90%)"],
        index=1,
        help="Choose GPU memory usage strategy"
    )
    st.session_state.gpu_memory_mode = memory_mode

# --- Processing ---
if st.sidebar.button("🚀 Load Data", use_container_width=True, type="primary"):
    if segy_file is not None:
        with st.spinner("Processing SEG-Y file with GPU optimization..."):
            # Create a temporary file to store the uploaded data
            with tempfile.NamedTemporaryFile(delete=False, suffix=".sgy") as tmp_file:
                tmp_file.write(segy_file.getvalue())
                st.session_state.segy_temp_file_path = tmp_file.name

            try:
                # Load headers using the cached function
                header_loader = load_segy_headers_cached(
                    st.session_state.segy_temp_file_path, inline_byte, xline_byte,
                    x_byte, y_byte, scaler_byte, use_custom_scaler
                )
                st.session_state.header_loader = header_loader

                # Get metadata
                st.session_state.dt = get_sampling_interval(st.session_state.segy_temp_file_path)
                st.session_state.trace_count = get_trace_count(st.session_state.segy_temp_file_path)

                # Estimate GPU memory usage
                if st.session_state.get('gpu_available'):
                    estimated_memory = estimate_gpu_memory_usage(st.session_state.trace_count)
                    st.session_state.estimated_gpu_memory = estimated_memory

                st.success("✅ SEG-Y file headers loaded successfully!")

                # Load well data if provided
                if well_file is not None:
                    st.session_state.well_data = load_excel_data_cached(well_file)
                    st.success("✅ Well data loaded successfully!")
                
            except Exception as e:
                st.error(f"❌ An error occurred while loading the SEG-Y file: {e}")
                logging.error(f"SEG-Y loading error: {e}", exc_info=True)

    else:
        st.sidebar.warning("⚠️ Please upload a SEG-Y file.")

# --- Display Info ---
if st.session_state.get('header_loader'):
    st.subheader("📊 Loaded Data Summary")
    
    # Basic data information
    info = st.session_state.header_loader.get_inline_crossline_range()
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Total Traces", f"{st.session_state.trace_count:,}")
        st.metric("Sampling Interval", f"{st.session_state.dt * 1000:.1f} ms")
    
    with col2:
        st.metric("Inline Range", f"{info['inline_min']} - {info['inline_max']}")
        st.metric("Crossline Range", f"{info['xline_min']} - {info['xline_max']}")
    
    with col3:
        if st.session_state.get('gpu_available') and st.session_state.get('estimated_gpu_memory'):
            st.metric("Est. GPU Memory", f"{st.session_state.estimated_gpu_memory:.1f} GB")
            memory_status = "✅ Suitable" if st.session_state.estimated_gpu_memory < 8.0 else "⚠️ Large Dataset"
            st.metric("Memory Status", memory_status)
        else:
            st.metric("Processing Mode", "CPU")
            st.metric("Memory Status", "N/A")

    # GPU Processing Recommendations
    if st.session_state.get('gpu_available'):
        st.markdown("### 🚀 GPU Processing Recommendations")
        
        if st.session_state.get('estimated_gpu_memory', 0) > 12.0:
            st.warning("⚠️ **Large Dataset Detected**: Consider using AOI analysis for better GPU memory management")
        elif st.session_state.get('estimated_gpu_memory', 0) > 8.0:
            st.info("💡 **Medium Dataset**: GPU processing will be efficient with batch optimization")
        else:
            st.success("✅ **Optimal Dataset Size**: Excellent for GPU acceleration")

    # Well data display
    if st.session_state.get('well_data') is not None:
        st.markdown("### 🎯 Well Data Preview")
        st.dataframe(st.session_state.well_data.head(), use_container_width=True)
        st.info(f"📊 Loaded {len(st.session_state.well_data)} well records")

    # Navigation guidance and controls
    st.markdown("### 👉 Next Steps")
    st.success("**✅ Data loaded successfully!** You can now proceed to the next step.")

    # Navigation buttons
    st.markdown("### 🧭 Navigation")
    col1, col2, col3 = st.columns([1, 1, 1])

    with col1:
        if st.button("🏠 Back to Dashboard", use_container_width=True, help="Return to main dashboard"):
            st.switch_page("app.py")

    with col2:
        if st.button("⚙️ Step 2: Configure Display", use_container_width=True, type="primary", help="Configure display parameters and processing settings"):
            st.switch_page("pages/2_configure_display.py")

    with col3:
        # Show Step 3 button but disable if Step 2 not completed
        if st.session_state.get('plot_settings'):
            if st.button("🎯 Step 3: Select Area", use_container_width=True, help="Select analysis area"):
                st.switch_page("pages/3_select_area.py")
        else:
            st.button("🎯 Step 3: Select Area", use_container_width=True, disabled=True, help="Complete Step 2 first")

    # Workflow progress indicator
    st.markdown("### 📊 Workflow Progress")
    progress_col1, progress_col2 = st.columns([3, 1])

    with progress_col1:
        # Calculate progress
        steps_completed = 1  # Step 1 is complete
        if st.session_state.get('plot_settings'):
            steps_completed += 1
        if st.session_state.get('area_selected'):
            steps_completed += 1
        if st.session_state.get('analysis_complete'):
            steps_completed += 1
        if st.session_state.get('export_complete'):
            steps_completed += 1

        progress_percentage = (steps_completed / 5) * 100
        st.progress(progress_percentage / 100)

    with progress_col2:
        st.metric("Completed", f"{steps_completed}/5 steps")

    # Step status indicators
    step_status_col1, step_status_col2, step_status_col3 = st.columns(3)

    with step_status_col1:
        st.success("✅ Step 1: Data Loaded")

    with step_status_col2:
        if st.session_state.get('plot_settings'):
            st.success("✅ Step 2: Display Configured")
        else:
            st.info("⏳ Step 2: Configure Display")

    with step_status_col3:
        if st.session_state.get('area_selected'):
            st.success("✅ Step 3: Area Selected")
        else:
            st.info("⏳ Step 3: Select Area")

else:
    st.info("📁 Upload a SEG-Y file and click '🚀 Load Data' to begin GPU-accelerated seismic analysis.")
    
    # Help section
    with st.expander("ℹ️ Help & Tips"):
        st.markdown("""
        **SEG-Y File Requirements:**
        - Standard SEG-Y format (.sgy or .segy)
        - Contains inline/crossline information in headers
        - Coordinate information for spatial analysis
        
        **GPU Acceleration Benefits:**
        - 10-50x faster processing for large datasets
        - Real-time parameter optimization
        - Efficient memory management for large surveys
        
        **Header Configuration:**
        - Use standard byte positions (189, 193, 73, 77) for most files
        - Adjust if your SEG-Y uses different header layout
        - Test with a small subset first if unsure
        """)

# --- Footer ---
st.markdown("---")
st.markdown("*WOSS Tool v2.0 - GPU-Accelerated Seismic Spectral Analysis*")
