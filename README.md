# WOSS Seismic Analysis Tool

GPU-Accelerated Spectral Analysis for Seismic Data

## Project Structure

### Main Pipeline
```
├── app.py                 # Main application entry point
├── pages/                 # Streamlit pages for the 5-step workflow
│   ├── 1_load_data.py    # Step 1: Load SEG-Y data
│   ├── 2_configure_display.py  # Step 2: Configure analysis parameters
│   ├── 3_select_area.py  # Step 3: Select analysis area
│   ├── 4_analyze_data.py # Step 4: Execute GPU-accelerated analysis
│   └── 5_export_results.py  # Step 5: Export results
├── common/               # Core session state and constants
│   ├── constants.py      # Application constants
│   └── session_state.py  # Session state management
├── utils/                # Core processing utilities
│   ├── data_utils.py     # Data handling utilities
│   ├── gpu_utils.py      # GPU acceleration utilities
│   ├── processing.py     # CPU processing functions
│   ├── processing_gpu.py # GPU processing functions
│   ├── visualization.py  # Visualization utilities
│   └── export_utils.py   # Export functionality
└── requirements.txt      # Python dependencies
```

### Supporting Folders
```
├── archive/              # Archived scripts unrelated to main pipeline
│   ├── app_ref.py       # Reference application version
│   ├── test_*.py        # Test scripts
│   └── woss_analysis.log # Log files
└── docs/                # Documentation and summaries
    └── summary/         # Development process documentation
        ├── guideline.md # Project guidelines
        ├── summary_step_*.md  # Development summaries
        └── next_phase_*.md    # Phase documentation
```

## Features

- **GPU Acceleration**: CUDA/CuPy support for faster processing
- **Multiple Analysis Modes**: Inline, crossline, AOI, polyline, and well markers
- **Comprehensive Outputs**: 14+ spectral attributes
- **Flexible Export**: SEG-Y, CSV, and Excel formats
- **Modular Architecture**: Clean separation of concerns
- **Interactive Workflow**: 5-step guided process

## Quick Start

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the application:
   ```bash
   streamlit run app.py
   ```

3. Follow the 5-step workflow:
   - Load Data → Configure Display → Select Area → Analyze Data → Export Results

## System Requirements

**Recommended:**
- NVIDIA GPU with CUDA support
- 8+ GB GPU memory for large datasets
- Python 3.8+ with CuPy installed

**Minimum:**
- CPU processing (fallback mode)
- 4+ GB system RAM
- Python 3.8+ with NumPy/SciPy

## Development

- **Main Pipeline**: Focus on files in root, `pages/`, `common/`, and `utils/`
- **Testing**: Test scripts are archived in `archive/` folder
- **Documentation**: Process documentation is in `docs/summary/`
