import numpy as np
import tkinter as tk
from tkinter import ttk, simpledialog, messagebox
from tqdm import tqdm
import sys
import segyio
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import logging
from .data_utils import load_trace_sample
from .general_utils import get_suggested_batch_size
# Try to import GPU functions first, then fallback to CPU implementations
try:
    from .dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu as dlogst_spec_descriptor
    GPU_AVAILABLE = True
    logging.info("Successfully imported GPU spectral descriptor functions in processing.py")
except ImportError as e:
    GPU_AVAILABLE = False
    logging.warning(f"Could not import GPU functions in processing.py: {e}. Using CPU fallback.")

    # Import CPU fallback implementations
    try:
        from .dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu as dlogst_spec_descriptor
        logging.info("Successfully imported CPU fallback spectral descriptor functions in processing.py")
    except ImportError as cpu_e:
        logging.error(f"Could not import CPU fallback functions in processing.py: {cpu_e}")
        # Define error function if both GPU and CPU fail
        def dlogst_spec_descriptor(*args, **kwargs):
            raise NotImplementedError("Neither GPU nor CPU spectral descriptor functions are available in processing.py.")

# --- Inserted Functions Start ---

def calculate_woss(descriptor, plot_settings):
    """
    Calculates WOSS from descriptor components using parameters from plot_settings.
    Matches logic from original script's export section.

    Args:
        descriptor: Dictionary containing spectral descriptors (hfc, norm_fdom, mag_voice_slope)
        plot_settings: Dictionary containing calculation parameters (epsilon, fdom_exponent, hfc_p95)

    Returns:
        numpy.ndarray: Calculated WOSS values
    """
    epsilon = plot_settings.get('epsilon', 1e-4)  # Default epsilon for numerical stability
    fdom_exponent = plot_settings.get('fdom_exponent', 2.0)
    # hfc_p95 needs to be provided or calculated beforehand
    hfc_p95 = plot_settings.get('hfc_p95', 1.0) # Get pre-calculated p95 if available

    required_keys = ['hfc', 'norm_fdom', 'mag_voice_slope']
    if not all(key in descriptor for key in required_keys):
        length = 0
        for key in required_keys:
            if key in descriptor and isinstance(descriptor[key], np.ndarray):
                length = len(descriptor[key])
                break
        if length == 0 and 'tf_map' in descriptor:
             length = descriptor['tf_map'].shape[0]
        # print(f"Warning: Missing keys for WOSS calculation ({[k for k in required_keys if k not in descriptor]}). Returning zeros array of length {length}.")
        return np.zeros(length, dtype=np.float32) if length > 0 else np.array([], dtype=np.float32)

    hfc_norm = descriptor['hfc'] / hfc_p95 if hfc_p95 != 0 else descriptor['hfc']
    norm_fdom = descriptor['norm_fdom']
    mag_voice_slope = descriptor['mag_voice_slope']

    # Check length consistency after ensuring keys exist
    if len(norm_fdom) != len(hfc_norm) or len(mag_voice_slope) != len(hfc_norm):
         # print(f"Length mismatch for WOSS calculation: hfc ({len(hfc_norm)}), norm_fdom ({len(norm_fdom)}), mv_slope ({len(mag_voice_slope)}). Returning zeros.")
         # Determine expected length safely
         expected_length = len(descriptor.get('tf_map', np.array([]))) # Fallback length
         if 'hfc' in descriptor: expected_length = len(descriptor['hfc'])

         return np.zeros(expected_length, dtype=np.float32) if expected_length > 0 else np.array([], dtype=np.float32)


    denominator = hfc_norm * (norm_fdom**fdom_exponent + epsilon)

    with np.errstate(divide='ignore', invalid='ignore'):
        woss = np.where(denominator > epsilon, mag_voice_slope / denominator, 0.0)
        woss[~np.isfinite(woss)] = 0.0
        woss = np.clip(woss, -1e6, 1e6)
    return woss.astype(np.float32)

# Helper function to clean array
def clean_array(arr, name="data"):
    if not isinstance(arr, np.ndarray) or arr.size == 0:
        # print(f"Warning: {name} array is empty or not an array.")
        return np.array([])
    if np.iscomplexobj(arr):
        # print(f"Warning: {name} contains complex values. Converting to real part only.")
        arr = np.real(arr)
    cleaned = arr[np.isfinite(arr)]
    if not np.isrealobj(cleaned):
        # print(f"Warning: {name} still contains non-real values after cleaning. Converting to float.")
        cleaned = cleaned.astype(float)
    # if cleaned.size == 0:
        # print(f"Warning: {name} contains no valid values after cleaning.")
    # else:
        # print(f"Cleaned {name}: {arr.size - cleaned.size} invalid values removed ({cleaned.size} values remain).")
    return cleaned

def calculate_stats_and_defaults(segy_path, header_loader, dt, sample_percent, max_traces_for_stats, **spectral_params):
    """
    Loads sample traces, calculates descriptors, computes statistics and default normalization values.
    This replaces the Tkinter-based get_plot_settings logic for Streamlit.

    Args:
        segy_path (str): Path to the SEG-Y file.
        header_loader (SegyHeaderLoader): Instance of the header loader.
        dt (float): Sampling interval.
        sample_percent (float): Percentage of traces to sample (0.1-100).
        max_traces_for_stats (int): Maximum number of traces to use for statistics.
        **spectral_params: Dictionary containing spectral parameters like
                           use_band_limited, shape, kmax, int_val, b1, b2,
                           p_bandwidth, roll_percent, epsilon, fdom_exponent.

    Returns:
        dict: A dictionary containing 'stats' (detailed statistics) and
              'defaults' (key normalization percentiles like hfc_p95, spec_decrease_p95).
              Returns None if calculation fails.
    """
    try:
        # Get trace count directly from the SEG-Y file
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            num_traces_total = segyfile.tracecount

        num_traces_to_sample = min(max(1, int(num_traces_total * (sample_percent / 100.0))), max_traces_for_stats)
        if num_traces_total == 0:
             print("Warning: SEG-Y file has 0 traces.")
             return None
        # Ensure num_traces_to_sample does not exceed num_traces_total
        num_traces_to_sample = min(num_traces_to_sample, num_traces_total)
        if num_traces_to_sample == 0:
             print("Warning: Calculated 0 traces to sample.")
             return None

        sample_indices = np.random.choice(num_traces_total, num_traces_to_sample, replace=False)

        print(f"Calculating stats based on {num_traces_to_sample} sample traces...")

        sample_descriptors = []
        max_len = 0
        # Load traces and find max length first
        loaded_samples = []
        for trace_idx in tqdm(sample_indices, desc="Loading sample traces"):
             try:
                 trace_sample = load_trace_sample(segy_path, trace_idx)
                 if trace_sample is not None and trace_sample.size > 0:
                     loaded_samples.append(trace_sample)
                     max_len = max(max_len, len(trace_sample))
                 else:
                     print(f"Warning: Skipping empty or invalid trace sample for index {trace_idx}")
             except Exception as e:
                 print(f"Warning: Error loading trace {trace_idx}: {e}")

        if not loaded_samples:
             print("Error: No valid sample traces could be loaded.")
             return None

        # Pad and calculate descriptors
        for trace_sample in tqdm(loaded_samples, desc="Calculating sample descriptors"):
            try:
                # Pad if necessary
                if max_len > 0 and len(trace_sample) < max_len:
                    pad_width = max_len - len(trace_sample)
                    trace_sample = np.pad(trace_sample, (0, pad_width), 'constant')
                elif max_len == 0: # Handle case where all traces might be empty initially
                     if trace_sample.size == 0: continue # Skip empty trace
                     max_len = len(trace_sample) # Set max_len based on first non-empty trace

                # Prepare settings for dlogst_spec_descriptor
                # Filter out epsilon parameter which is only used for WOSS calculation
                descriptor_settings = {
                    'use_band_limited': spectral_params.get('use_band_limited', False),
                    'shape': spectral_params.get('shape', 1.0),
                    'kmax': spectral_params.get('kmax', 1.0),
                    'int_val': spectral_params.get('int_val', 1.0),
                    'b1': spectral_params.get('b1', 0.01),
                    'b2': spectral_params.get('b2', 0.1),
                    'p_bandwidth': spectral_params.get('p_bandwidth', 0.5),
                    'roll_percent': spectral_params.get('roll_percent', 0.85)
                }
                # Remove epsilon and other WOSS-specific parameters that aren't used by dlogst_spec_descriptor
                for param in ['epsilon', 'fdom_exponent']:
                    if param in descriptor_settings:
                        del descriptor_settings[param]

                fmax_calc = max_len // 2 if max_len > 0 else 250 # Estimate fmax

                descriptor = dlogst_spec_descriptor(trace_sample, dt, fmax=fmax_calc, **descriptor_settings)
                descriptor['data'] = trace_sample # Add original (padded) data
                sample_descriptors.append(descriptor)
            except Exception as e:
                print(f"Warning: Error calculating descriptor for a sample trace: {e}")

        if not sample_descriptors:
            print("Error: Failed to calculate descriptors for any sample traces.")
            return None

        print(f"Calculated descriptors for {len(sample_descriptors)} samples.")

        # --- Calculate Statistics and Percentiles ---
        stats_results = {}
        percentile_defaults = {}
        data_arrays = {}

        stat_keys = [
            "data", "hfc", "spec_decrease", "spec_slope", "mag_voice_slope",
            "spec_bandwidth", "spec_rolloff", "mag", "mag_voice", "norm_fdom"
        ]
        display_names = {
            "data": "Input Signal", "hfc": "HFC", "spec_decrease": "Spectral Decrease",
            "spec_slope": "Spectral Slope", "mag_voice_slope": "Mag*Voice Slope",
            "spec_bandwidth": "Spectral Bandwidth", "spec_rolloff": "Spectral Rolloff",
            "mag": "Magnitude Spectrogram", "mag_voice": "Magnitude * Voice",
            "norm_fdom": "Normalized Dominant Frequency"
        }

        for key in stat_keys:
            arr_list = []
            for d in sample_descriptors:
                if key in d and isinstance(d[key], np.ndarray) and d[key].size > 0:
                    arr_list.append(d[key].flatten())
            if arr_list:
                 data_arrays[key] = clean_array(np.concatenate(arr_list), display_names.get(key, key))
            else:
                 data_arrays[key] = np.array([])

        for key, arr in data_arrays.items():
            display_name = display_names.get(key, key)
            stats_results[display_name] = {}
            if arr.size > 0:
                try:
                    p = [1, 5, 10, 25, 50, 75, 90, 95, 99]
                    percentiles = np.percentile(arr, p)
                    stats_results[display_name] = {
                        "Mean": f"{np.mean(arr):.4f}",
                        "Median": f"{np.median(arr):.4f}",
                        "Std Dev": f"{np.std(arr):.4f}",
                        "Min": f"{np.min(arr):.4f}",
                        "Max": f"{np.max(arr):.4f}",
                        "P1": f"{percentiles[0]:.4f}",
                        "P5": f"{percentiles[1]:.4f}",
                        "P10": f"{percentiles[2]:.4f}",
                        "P25": f"{percentiles[3]:.4f}",
                        "P50": f"{percentiles[4]:.4f}",
                        "P75": f"{percentiles[5]:.4f}",
                        "P90": f"{percentiles[6]:.4f}",
                        "P95": f"{percentiles[7]:.4f}",
                        "P99": f"{percentiles[8]:.4f}",
                    }
                    percentile_defaults[f"{key}_p5"] = percentiles[1]
                    percentile_defaults[f"{key}_p95"] = percentiles[7]
                    if key in ["data", "mag_voice_slope"]:
                         abs_p95 = np.percentile(np.abs(arr), 95)
                         percentile_defaults[f"{key}_p95_abs"] = abs_p95
                except Exception as e:
                    print(f"Error calculating stats for {display_name}: {e}")
                    stats_results[display_name] = {"Error": str(e)}
            else:
                stats_results[display_name] = {"Info": "No valid data"}

        # --- Calculate WOSS for samples ---
        hfc_p95_default = percentile_defaults.get('hfc_p95', 1.0)
        woss_settings = {
             'epsilon': spectral_params.get('epsilon', 1e-4),  # Updated default epsilon
             'fdom_exponent': spectral_params.get('fdom_exponent', 2.0),
             'hfc_p95': hfc_p95_default
        }
        woss_values = []
        for d in sample_descriptors:
             if all(k in d for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                 woss = calculate_woss(d, woss_settings)
                 woss_values.append(woss)

        woss_combined = clean_array(np.concatenate(woss_values) if woss_values else np.array([]), "WOSS")
        data_arrays["woss"] = woss_combined

        display_name = "WOSS"
        stats_results[display_name] = {}
        if woss_combined.size > 0:
            try:
                p = [1, 5, 10, 25, 50, 75, 90, 95, 99]
                percentiles = np.percentile(woss_combined, p)
                stats_results[display_name] = {
                    "Mean": f"{np.mean(woss_combined):.4f}",
                    "Median": f"{np.median(woss_combined):.4f}",
                    "Std Dev": f"{np.std(woss_combined):.4f}",
                    "Min": f"{np.min(woss_combined):.4f}",
                    "Max": f"{np.max(woss_combined):.4f}",
                    "P1": f"{percentiles[0]:.4f}",
                    "P5": f"{percentiles[1]:.4f}",
                    "P10": f"{percentiles[2]:.4f}",
                    "P25": f"{percentiles[3]:.4f}",
                    "P50": f"{percentiles[4]:.4f}",
                    "P75": f"{percentiles[5]:.4f}",
                    "P90": f"{percentiles[6]:.4f}",
                    "P95": f"{percentiles[7]:.4f}",
                    "P99": f"{percentiles[8]:.4f}",
                }
                percentile_defaults['woss_p5'] = percentiles[1]
                percentile_defaults['woss_p95'] = percentiles[7]
                abs_p95 = np.percentile(np.abs(woss_combined), 95)
                percentile_defaults['woss_p95_abs'] = abs_p95
            except Exception as e:
                print(f"Error calculating stats for {display_name}: {e}")
                stats_results[display_name] = {"Error": str(e)}
        else:
            stats_results[display_name] = {"Info": "No valid data"}

        final_defaults = {
            'hfc_p95': percentile_defaults.get('hfc_p95', 1.0),
            'spec_decrease_p95': percentile_defaults.get('spec_decrease_p95', 1.0),
            'data_p5': percentile_defaults.get('data_p5', 0.0),
            'data_p95': percentile_defaults.get('data_p95', 1.0),
            'woss_p5': percentile_defaults.get('woss_p5', -1.0),
            'woss_p95': percentile_defaults.get('woss_p95', 1.0),
        }

        return {'stats': stats_results, 'defaults': final_defaults}

    except Exception as e:
        print(f"Error in calculate_stats_and_defaults: {e}")
        # import traceback
        # print(traceback.format_exc())
        return None

# --- Inserted Functions End ---
# Custom exception handler to ensure proper error display in GUI environments
def custom_excepthook(exc_type, exc_value, exc_traceback):
    """Custom exception handler that sanitizes error messages for GUI display.

    This function replaces backticks with single quotes in error messages to prevent
    formatting issues in GUI message boxes and ensures all exceptions are properly logged.

    Args:
        exc_type: Exception type
        exc_value: Exception value/message
        exc_traceback: Exception traceback object
    """
    try:
        # Replace any backticks in error messages with quotes
        if isinstance(exc_value, str):
            exc_value = exc_value.replace("`", "'")
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
    except Exception as e:
        print(f"Error in custom exception handler: {e}")
        sys.__excepthook__(exc_type, exc_value, exc_traceback)

# Register the custom exception handler
sys.excepthook = custom_excepthook

def get_general_and_spectral_settings(dt, num_samples, root=None):
    """
    Display a dialog for configuring general and spectral analysis parameters.

    This function creates a GUI dialog with tabs for configuring various parameters
    related to spectral analysis, including numerical stability settings, sampling
    parameters, frequency bands, and logistic transform settings.

    Args:
        dt: Sampling interval in seconds
        num_samples: Number of samples in each trace
        root: Optional Tkinter root window. If None, a new Tk instance will be created.

    Returns:
        dict: Dictionary containing all configured parameters
    """
    # If root is not provided, create a new Tk instance
    if root is None:
        root = tk.Tk()
        root.withdraw()

    dialog = tk.Toplevel(root)
    dialog.title("Set General and Spectral Analysis Parameters")
    settings = {}
    limit_entries = {}
    spectral_entries = {}
    settings_set = tk.BooleanVar(value=False)

    notebook = ttk.Notebook(dialog)
    notebook.pack(fill='both', expand=True, padx=10, pady=10)

    # General Settings Tab
    general_tab = ttk.Frame(notebook)
    notebook.add(general_tab, text="General Settings")

    row = 0
    ttk.Label(general_tab, text="Numerical Stability Settings:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=4, pady=(10, 0), sticky='w')
    row += 1
    ttk.Label(general_tab, text="Epsilon:").grid(row=row, column=0, sticky="e")
    epsilon_entry = ttk.Entry(general_tab)
    epsilon_entry.insert(0, "1e-10")
    epsilon_entry.grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
    row += 1

    ttk.Label(general_tab, text="Sampling Settings:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=4, pady=(10, 0), sticky='w')
    row += 1
    ttk.Label(general_tab, text="Sampling Percentage (0.1-100):").grid(row=row, column=0, sticky="e")
    sample_percent_entry = ttk.Entry(general_tab)
    sample_percent_entry.insert(0, "1.0")
    sample_percent_entry.grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
    row += 1
    ttk.Label(general_tab, text="Maximum Traces:").grid(row=row, column=0, sticky="e")
    max_traces_entry = ttk.Entry(general_tab)
    max_traces_entry.insert(0, "50")  # Set default maximum traces to 50
    max_traces_entry.grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
    row += 1

    ttk.Label(general_tab, text="Plot Limits:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=4, pady=(10, 0), sticky='w')
    row += 1
    fields = [("Frequency (Hz)", (0, 100)), ("Time (Y-axis) (s)", (0, (num_samples - 1) * dt))]
    for name, default in fields:
        ttk.Label(general_tab, text=f"{name}:").grid(row=row, column=0, sticky="e")
        min_entry = ttk.Entry(general_tab, width=8)
        min_entry.insert(0, str(default[0]))
        min_entry.grid(row=row, column=1, padx=5, pady=2)
        ttk.Label(general_tab, text="to").grid(row=row, column=2)
        max_entry = ttk.Entry(general_tab, width=8)
        max_entry.insert(0, str(default[1]))
        max_entry.grid(row=row, column=3, padx=5, pady=2)
        limit_entries[name] = (min_entry, max_entry)
        row += 1

    # Spectral Analysis Tab
    spectral_tab = ttk.Frame(notebook)
    notebook.add(spectral_tab, text="Spectral Analysis")

    row = 0
    ttk.Label(spectral_tab, text="Frequency Band Settings:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=4, pady=(10, 0), sticky='w')
    row += 1
    freq_band_fields = [("b1 (Hz)", 5), ("b2 (Hz)", 40)]
    for name, default in freq_band_fields:
        ttk.Label(spectral_tab, text=name).grid(row=row, column=0, sticky="e")
        entry = ttk.Entry(spectral_tab)
        entry.insert(0, str(default))
        entry.grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
        spectral_entries[name.split()[0]] = entry
        row += 1

    ttk.Label(spectral_tab, text="Spectral Slope/Decrease Mode:").grid(row=row, column=0, sticky="e")
    band_limited_var = tk.StringVar(spectral_tab)
    band_limited_var.set("Full-Band")
    ttk.OptionMenu(spectral_tab, band_limited_var, "Full-Band", "Full-Band", "Band-Limited").grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
    row += 1

    ttk.Label(spectral_tab, text="Logistic Transform Settings:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=4, pady=(10, 0), sticky='w')
    row += 1
    logistic_fields = [("kmax", 120), ("shape", 0.35), ("int_val", 35)]
    for name, default in logistic_fields:
        ttk.Label(spectral_tab, text=name).grid(row=row, column=0, sticky="e")
        entry = ttk.Entry(spectral_tab)
        entry.insert(0, str(default))
        entry.grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
        spectral_entries[name] = entry
        row += 1

    ttk.Label(spectral_tab, text="Bandwidth and Rolloff Settings:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=4, pady=(10, 0), sticky='w')
    row += 1
    bandwidth_fields = [("p_bandwidth", 2), ("roll_percent", 0.85)]
    for name, default in bandwidth_fields:
        ttk.Label(spectral_tab, text=name).grid(row=row, column=0, sticky="e")
        entry = ttk.Entry(spectral_tab)
        entry.insert(0, str(default))
        entry.grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
        spectral_entries[name] = entry
        row += 1

    ttk.Label(spectral_tab, text="WOSS Formula Settings:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=4, pady=(10, 0), sticky='w')
    row += 1
    ttk.Label(spectral_tab, text="Dominant Frequency Exponent:").grid(row=row, column=0, sticky="e")
    fdom_exp_entry = ttk.Entry(spectral_tab)
    fdom_exp_entry.insert(0, "2.0")
    fdom_exp_entry.grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
    spectral_entries["fdom_exponent"] = fdom_exp_entry

    def on_ok():
        try:
            settings['Frequency'] = (float(limit_entries['Frequency (Hz)'][0].get()), float(limit_entries['Frequency (Hz)'][1].get()))
            settings['Time (Y-axis)'] = (float(limit_entries['Time (Y-axis) (s)'][0].get()), float(limit_entries['Time (Y-axis) (s)'][1].get()))
            settings['epsilon'] = float(epsilon_entry.get())
            settings['sample_percent'] = float(sample_percent_entry.get())
            settings['max_traces'] = int(max_traces_entry.get())
            settings['b1'] = float(spectral_entries['b1'].get())
            settings['b2'] = float(spectral_entries['b2'].get())
            settings['kmax'] = float(spectral_entries['kmax'].get())
            settings['shape'] = float(spectral_entries['shape'].get())
            settings['int_val'] = float(spectral_entries['int_val'].get())
            settings['p_bandwidth'] = float(spectral_entries['p_bandwidth'].get())
            settings['roll_percent'] = float(spectral_entries['roll_percent'].get())
            settings['fdom_exponent'] = float(spectral_entries['fdom_exponent'].get())
            settings['use_band_limited'] = band_limited_var.get() == "Band-Limited"
            settings_set.set(True)
            dialog.destroy()
        except ValueError:
            messagebox.showerror("Input Error", "Please enter valid numerical values.")
            return

    ttk.Button(dialog, text="OK", command=on_ok).pack(pady=10)
    dialog.wait_window()

    if not settings_set.get():
        settings = {
            'Frequency': (0, 100),
            'Time (Y-axis)': (0, (num_samples - 1) * dt),
            'epsilon': 1e-10,
            'sample_percent': 1.0,
            'max_traces': 1000,
            'b1': 5, 'b2': 40,
            'kmax': 120, 'shape': 0.35, 'int_val': 35,
            'p_bandwidth': 2, 'roll_percent': 0.85,
            'fdom_exponent': 2.0,
            'use_band_limited': False
        }

    return settings

def get_statistics_percentiles_and_display_settings(descriptors, general_spectral_settings, root=None):
    # If root is not provided, create a new Tk instance
    if root is None:
        root = tk.Tk()
        root.withdraw()

    dialog = tk.Toplevel(root)
    dialog.title("Set Statistics, Percentile, and Display Settings")
    settings = {}
    limit_entries = {}
    settings_set = tk.BooleanVar(value=False)

    notebook = ttk.Notebook(dialog)
    notebook.pack(fill='both', expand=True, padx=10, pady=10)

    # Statistics Tab
    stats_tab = ttk.Frame(notebook)
    notebook.add(stats_tab, text="Statistics")

    stats_text = tk.Text(stats_tab, wrap=tk.WORD, height=25, width=50)
    stats_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # Helper function to clean array of inf/nan and check if valid
    def clean_array(arr, name="data"):
        if arr.size == 0:
            print(f"Warning: {name} array is empty.")
            return np.array([])

        if np.iscomplexobj(arr):
            print(f"Warning: {name} contains complex values. Converting to real part only.")
            arr = np.real(arr)

        cleaned = arr[np.isfinite(arr)]

        if not np.isrealobj(cleaned):
            print(f"Warning: {name} still contains non-real values after cleaning. Converting to float.")
            cleaned = cleaned.astype(float)

        if cleaned.size == 0:
            print(f"Warning: {name} contains no valid values after cleaning.")
        else:
            print(f"Cleaned {name}: {arr.size - cleaned.size} invalid values removed ({cleaned.size} values remain).")

        return cleaned

    # Extract and clean input signal data
    input_signal_raw = np.concatenate([d['data'] for d in descriptors if isinstance(d['data'], np.ndarray) and d['data'].size > 0]) if descriptors else np.array([])
    input_signal = clean_array(input_signal_raw, "Input Signal")

    # Calculate percentiles for all data arrays for use as default min/max values
    data_arrays = {
        "Input Signal": input_signal,
        "HFC": clean_array(np.concatenate([d['hfc'] for d in descriptors if isinstance(d['hfc'], np.ndarray) and d['hfc'].size > 0]) if descriptors else np.array([]), "HFC"),
        "Spectral Decrease": clean_array(np.concatenate([d['spec_decrease'] for d in descriptors if isinstance(d['spec_decrease'], np.ndarray) and d['spec_decrease'].size > 0]) if descriptors else np.array([]), "Spectral Decrease"),
        "Spectral Slope": clean_array(np.concatenate([d['spec_slope'] for d in descriptors if isinstance(d['spec_slope'], np.ndarray) and d['spec_slope'].size > 0]) if descriptors else np.array([]), "Spectral Slope"),
        "Mag*Voice Slope": clean_array(np.concatenate([d['mag_voice_slope'] for d in descriptors if isinstance(d['mag_voice_slope'], np.ndarray) and d['mag_voice_slope'].size > 0]) if descriptors else np.array([]), "Mag*Voice Slope"),
        "Spectral Bandwidth": clean_array(np.concatenate([d['spec_bandwidth'] for d in descriptors if isinstance(d['spec_bandwidth'], np.ndarray) and d['spec_bandwidth'].size > 0]) if descriptors else np.array([]), "Spectral Bandwidth"),
        "Spectral Rolloff": clean_array(np.concatenate([d['spec_rolloff'] for d in descriptors if isinstance(d['spec_rolloff'], np.ndarray) and d['spec_rolloff'].size > 0]) if descriptors else np.array([]), "Spectral Rolloff"),
        # Add Magnitude Spectrogram data extraction
        "Magnitude Spectrogram": clean_array(np.concatenate([d['mag'].flatten() for d in descriptors if 'mag' in d and isinstance(d['mag'], np.ndarray) and d['mag'].size > 0]) if descriptors else np.array([]), "Magnitude Spectrogram"),
        "Magnitude * Voice": clean_array(np.concatenate([d['mag_voice'].flatten() for d in descriptors if 'mag_voice' in d and isinstance(d['mag_voice'], np.ndarray) and d['mag_voice'].size > 0]) if descriptors else np.array([]), "Magnitude * Voice") # Use actual data if available
    }

    # Calculate percentiles and store for later use as default min/max values
    percentile_defaults = {}
    for name, arr in data_arrays.items():
        if arr.size > 0:
            try:
                # Use P1 and P99 for spectrograms as they often have wider dynamic range
                if name in ["Magnitude Spectrogram", "Magnitude * Voice"]:
                     p_low, p_high = 1, 99
                else:
                     p_low, p_high = 5, 95

                val_low = np.percentile(arr, p_low)
                val_high = np.percentile(arr, p_high)
                percentile_defaults[name] = (val_low, val_high)
                print(f"Calculated {name} P{p_low}/P{p_high} defaults: ({val_low:.3f}, {val_high:.3f})")


                # Keep symmetric calculation for relevant attributes
                if name in ["Input Signal", "Mag*Voice Slope", "WOSS"]:
                    abs_p95 = np.percentile(np.abs(arr), 95)
                    percentile_defaults[f"{name} (symmetric)"] = (-abs_p95, abs_p95)
            except Exception as e:
                print(f"Error calculating percentiles for {name}: {e}")
                percentile_defaults[name] = (0, 1) # Fallback
        else:
             # Set specific fallbacks if no data
            if name == "Magnitude Spectrogram":
                percentile_defaults[name] = (0, 1) # Sensible default for magnitude
            elif name == "Magnitude * Voice":
                percentile_defaults[name] = (-1, 1) # Symmetric default
            else:
                percentile_defaults[name] = (0, 1)
            print(f"No valid data for {name}, using default limits: {percentile_defaults[name]}")


    if "WOSS" not in percentile_defaults:
        percentile_defaults["WOSS"] = (-3, 3)
        percentile_defaults["WOSS (symmetric)"] = (-3, 3)

    if input_signal.size > 0:
        try:
            signal_percentiles = np.percentile(input_signal, [1, 5, 25, 50, 75, 95, 99])
            stats_text.insert(tk.END, "Input Signal Statistics:\n\n")
            stats_text.insert(tk.END, f"  Mean: {np.mean(input_signal):.4f}\n")
            stats_text.insert(tk.END, f"  Median: {np.median(input_signal):.4f}\n")
            stats_text.insert(tk.END, f"  Std Dev: {np.std(input_signal):.4f}\n")
            stats_text.insert(tk.END, f"  Min: {np.min(input_signal):.4f}\n")
            stats_text.insert(tk.END, f"  Max: {np.max(input_signal):.4f}\n")
            stats_text.insert(tk.END, f"  1st Percentile: {signal_percentiles[0]:.4f}\n")
            stats_text.insert(tk.END, f"  5th Percentile: {signal_percentiles[1]:.4f}\n")
            stats_text.insert(tk.END, f"  25th Percentile: {signal_percentiles[2]:.4f}\n")
            stats_text.insert(tk.END, f"  75th Percentile: {signal_percentiles[4]:.4f}\n")
            stats_text.insert(tk.END, f"  95th Percentile: {signal_percentiles[5]:.4f}\n")
            stats_text.insert(tk.END, f"  99th Percentile: {signal_percentiles[6]:.4f}\n")
            stats_text.insert(tk.END, "\n")
        except (TypeError, ValueError) as e:
            print(f"Error calculating statistics for Input Signal: {e}")
            stats_text.insert(tk.END, "Input Signal Statistics:\n\n")
            stats_text.insert(tk.END, f"  Error calculating statistics: {e}\n\n")
    else:
        stats_text.insert(tk.END, "Input Signal Statistics:\n\n")
        stats_text.insert(tk.END, "  No valid finite data available after cleaning inf/nan.\n\n")

    # Add spectral descriptor statistics with cleaning
    hfc = data_arrays["HFC"]
    spec_decrease = data_arrays["Spectral Decrease"]
    spec_slope = data_arrays["Spectral Slope"]
    mag_voice_slope = data_arrays["Mag*Voice Slope"]
    spec_bandwidth = data_arrays["Spectral Bandwidth"]
    spec_rolloff = data_arrays["Spectral Rolloff"]

    stats_text.insert(tk.END, "Spectral Descriptors Statistics:\n\n")
    for name, values in [
        ("HFC", hfc),
        ("Spectral Decrease", spec_decrease),
        ("Spectral Slope", spec_slope),
        ("Mag*Voice Slope", mag_voice_slope),
        ("Spectral Bandwidth", spec_bandwidth),
        ("Spectral Rolloff", spec_rolloff)
    ]:
        if values.size > 0:
            try:
                descriptor_percentiles = np.percentile(values, [5, 10, 25, 50, 75, 90, 95])
                stats_text.insert(tk.END, f"{name}:\n"
                                f"  Mean: {np.mean(values):.2f}\n"
                                f"  Median: {np.median(values):.2f}\n"
                                f"  Std Dev: {np.std(values):.2f}\n"
                                f"  Min: {np.min(values):.2f}\n"
                                f"  Max: {np.max(values):.2f}\n"
                                f"  5th Percentile: {descriptor_percentiles[0]:.2f}\n"
                                f"  10th Percentile: {descriptor_percentiles[1]:.2f}\n"
                                f"  25th Percentile: {descriptor_percentiles[2]:.2f}\n"
                                f"  50th Percentile: {descriptor_percentiles[3]:.2f}\n"
                                f"  75th Percentile: {descriptor_percentiles[4]:.2f}\n"
                                f"  90th Percentile: {descriptor_percentiles[5]:.2f}\n"
                                f"  95th Percentile: {descriptor_percentiles[6]:.2f}\n\n")
            except (TypeError, ValueError) as e:
                print(f"Error calculating statistics for {name}: {e}")
                stats_text.insert(tk.END, f"{name}:\n  Error calculating statistics: {e}\n\n")
        else:
            stats_text.insert(tk.END, f"{name}:\n  No valid data available after cleaning.\n\n")

    # Add WOSS statistics calculation and display (for plot limit estimation)
    epsilon = general_spectral_settings.get('epsilon', 1e-10)
    fdom_exponent = general_spectral_settings.get('fdom_exponent', 2.0)

    # Calculate WOSS using hfc_p95 instead of hardcoded 1.0 for normalization
    woss_values = []
    # Get the 95th percentile of HFC values if available, otherwise fallback to 1.0
    hfc_p95 = np.percentile(data_arrays["HFC"], 95) if data_arrays["HFC"].size > 0 else 1.0
    for d in descriptors:
        if (isinstance(d['hfc'], np.ndarray) and d['hfc'].size > 0 and
            isinstance(d['norm_fdom'], np.ndarray) and d['norm_fdom'].size > 0 and
            isinstance(d['mag_voice_slope'], np.ndarray) and d['mag_voice_slope'].size > 0):

            hfc_norm = d['hfc'] / hfc_p95  # Now using hfc_p95 instead of hardcoded 1.0
            norm_fdom = d['norm_fdom']  # Normalized dominant frequency
            mv_slope = d['mag_voice_slope']    # Magnitude*Voice slope

            # Calculate denominator with protection against division by zero
            denominator = hfc_norm * (norm_fdom**fdom_exponent + epsilon)

            # Calculate WOSS with protection against invalid values
            with np.errstate(divide='ignore', invalid='ignore'):
                woss = np.where(denominator > epsilon, mv_slope / denominator, 0.0)
                woss[~np.isfinite(woss)] = 0.0
                woss = np.clip(woss, -1e6, 1e6)
                woss_values.append(woss)

    # Combine all WOSS values
    woss_combined = np.concatenate(woss_values) if woss_values else np.array([])

    # Add WOSS to data_arrays for percentile calculation
    data_arrays["WOSS"] = clean_array(woss_combined, "WOSS")

    # Display WOSS statistics
    if woss_combined.size > 0:
        try:
            woss_percentiles = np.percentile(woss_combined, [5, 10, 25, 50, 75, 90, 95])
            stats_text.insert(tk.END, f"WOSS (fdom^{fdom_exponent:.1f}, using HFC p95={hfc_p95:.4f}):\n"
                           f"  Mean: {np.mean(woss_combined):.2f}\n"
                           f"  Median: {np.median(woss_combined):.2f}\n"
                           f"  Std Dev: {np.std(woss_combined):.2f}\n"
                           f"  Min: {np.min(woss_combined):.2f}\n"
                           f"  Max: {np.max(woss_combined):.2f}\n"
                           f"  5th Percentile: {woss_percentiles[0]:.2f}\n"
                           f"  10th Percentile: {woss_percentiles[1]:.2f}\n"
                           f"  25th Percentile: {woss_percentiles[2]:.2f}\n"
                           f"  50th Percentile: {woss_percentiles[3]:.2f}\n"
                           f"  75th Percentile: {woss_percentiles[4]:.2f}\n"
                           f"  90th Percentile: {woss_percentiles[5]:.2f}\n"
                           f"  95th Percentile: {woss_percentiles[6]:.2f}\n\n")

            # Update symmetric percentile defaults for WOSS
            abs_p95 = np.percentile(np.abs(woss_combined), 95)
            percentile_defaults["WOSS"] = (woss_percentiles[0], woss_percentiles[-1])
            percentile_defaults["WOSS (symmetric)"] = (-abs_p95, abs_p95)
        except Exception as e:
            print(f"Error calculating statistics for WOSS: {e}")
            stats_text.insert(tk.END, f"WOSS (fdom^{fdom_exponent:.1f}, using HFC p95={hfc_p95:.4f}):\n  Error calculating statistics: {e}\n\n")
    else:
        stats_text.insert(tk.END, f"WOSS (fdom^{fdom_exponent:.1f}, using HFC p95={hfc_p95:.4f}):\n  No valid data available after cleaning.\n\n")

    stats_text.config(state=tk.DISABLED)

    # Plot Limits Tab
    limits_tab = ttk.Frame(notebook)
    notebook.add(limits_tab, text="Plot Limits")

    limits_frame = ttk.Frame(limits_tab, padding=10)
    limits_frame.pack(fill=tk.BOTH, expand=True)

    ttk.Label(limits_frame, text="Parameter", font=("Arial", 10, "bold")).grid(row=0, column=0, padx=5, pady=5, sticky='w')
    ttk.Label(limits_frame, text="Min (P5)", font=("Arial", 10, "bold")).grid(row=0, column=1, padx=5, pady=5)
    ttk.Label(limits_frame, text="Max (P95)", font=("Arial", 10, "bold")).grid(row=0, column=2, padx=5, pady=5)

    limit_pairs = [
        ("Input Signal", "Input Signal"),
        ("Magnitude Spectrogram", "Magnitude Spectrogram"), # Add Magnitude Spectrogram
        ("Magnitude * Voice", "Magnitude * Voice"),
        ("Spectral Slope", "Spectral Slope"),
        ("Spectral Bandwidth", "Spectral Bandwidth"),
        ("Spectral Rolloff", "Spectral Rolloff"),
        ("Mag*Voice Slope", "Mag*Voice Slope"),
        ("WOSS", "WOSS")
    ]

    for idx, (key, label) in enumerate(limit_pairs, 1):
        ttk.Label(limits_frame, text=label).grid(row=idx, column=0, padx=5, pady=5, sticky='w')

        # Use calculated P1/P99 or P5/P95 defaults
        if label in percentile_defaults:
            default_min, default_max = percentile_defaults[label]
        else: # Fallback if calculation failed
            if label == "Magnitude Spectrogram": default_min, default_max = (0, 1)
            elif label == "Magnitude * Voice": default_min, default_max = (-1, 1)
            elif label in ["Spectral Bandwidth", "Spectral Rolloff"]: default_min, default_max = (0, 1)
            else: default_min, default_max = (-1, 1)
            print(f"Warning: Using fallback limits for {label}")


        min_entry = ttk.Entry(limits_frame, width=10)
        min_entry.insert(0, f"{default_min:.3f}")
        min_entry.grid(row=idx, column=1, padx=5, pady=5)

        max_entry = ttk.Entry(limits_frame, width=10)
        max_entry.insert(0, f"{default_max:.3f}")
        max_entry.grid(row=idx, column=2, padx=5, pady=5)

        limit_entries[key] = (min_entry, max_entry)

    next_row = len(limit_pairs) + 1
    ttk.Label(limits_frame, text="Global Normalization", font=("Arial", 10, "bold")).grid(row=next_row, column=0, columnspan=3, padx=5, pady=5, sticky='w')
    next_row += 1

    percentile_entries = {}
    single_percentiles = [("hfc", "HFC Normalization", 95), ("spec_decrease", "Spec Decrease Normalization", 95)]
    for key, label, default in single_percentiles:
        ttk.Label(limits_frame, text=label).grid(row=next_row, column=0, padx=5, pady=5, sticky='w')
        entry = ttk.Entry(limits_frame, width=8)
        entry.insert(0, str(default))
        entry.grid(row=next_row, column=1, padx=5, pady=5)
        ttk.Label(limits_frame, text="%").grid(row=next_row, column=2, padx=0, pady=5, sticky='w')
        percentile_entries[key] = entry
        next_row += 1

    # Display Settings Tab
    display_tab = ttk.Frame(notebook)
    notebook.add(display_tab, text="Display Settings")

    row = 0
    plotly_colorscales = ['RdBu', 'viridis', 'plasma', 'inferno', 'magma', 'cividis', 'hot', 'jet', 'RdYlBu', 'RdYlGn', 'RdGy', 'Spectral', 'hsv', 'icefire', 'BrBG', 'rainbow']
    ttk.Label(display_tab, text="Colormap Settings:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=2, pady=(10, 0), sticky='w')
    row += 1

    colormap_settings = [
        ("Magnitude Spectrogram", "colormap", "rainbow"),
        ("Magnitude * Voice", "colormap_mag_voice", "rainbow"),
        ("Dominant Frequency", "colormap_dom_freq", "rainbow"),
        ("Normalized Spectral Decrease", "colormap_spec_decrease", "rainbow"),
        ("Normalized HFC", "colormap_hfc", "rainbow"),
        ("Spectral Bandwidth", "colormap_bandwidth", "rainbow"),
        ("Spectral Rolloff", "colormap_rolloff", "rainbow"),
        ("WOSS", "colormap_woss", "RdBu"),  # Use RdBu for WOSS
        ("Seismic Sections", "section_colormap", "RdBu"),  # Use RdBu for Input Signal
        ("Mag*Voice Slope", "colormap_mag_voice_slope", "RdBu")  # Use RdBu for Magnitude * Voice Slope
    ]

    colormap_vars = {}
    for label, key, default in colormap_settings:
        ttk.Label(display_tab, text=f"{label}:").grid(row=row, column=0, sticky="e")
        var = tk.StringVar(dialog)
        var.set(default)
        ttk.OptionMenu(display_tab, var, default, *plotly_colorscales).grid(row=row, column=1, sticky="ew", pady=2, padx=5)
        colormap_vars[key] = var
        row += 1

    def on_ok():
        try:
            for key, entry_pair in limit_entries.items():
                min_val, max_val = float(entry_pair[0].get()), float(entry_pair[1].get())
                if min_val > max_val:
                    raise ValueError(f"Min value must be less than or equal to max value for {key}.")
                settings[key] = (min_val, max_val)

            for key, entry in percentile_entries.items():
                val = float(entry.get())
                if not (0 <= val <= 100):
                    raise ValueError(f"Percentile value for {key} must be 0-100.")
                settings[f"{key}_percentile"] = val

            for key, var in colormap_vars.items():
                settings[key] = var.get()

            settings_set.set(True)
            dialog.destroy()
        except ValueError as e:
            error_msg = str(e).replace("`", "'")
            messagebox.showerror("Input Error", error_msg)
            return

    ttk.Button(dialog, text="OK", command=on_ok).pack(pady=10)
    dialog.wait_window()

    if not settings_set.get():
        settings = {
            'Input Signal': percentile_defaults.get("Input Signal", (-1, 1)),
            'Magnitude Spectrogram': percentile_defaults.get("Magnitude Spectrogram", (0, 1)), # Add fallback
            'Magnitude * Voice': percentile_defaults.get("Magnitude * Voice", (-1, 1)),
            'Spectral Slope': percentile_defaults.get("Spectral Slope", (-1, 1)),
            'Spectral Bandwidth': percentile_defaults.get("Spectral Bandwidth", (0, 50)),
            'Spectral Rolloff': percentile_defaults.get("Spectral Rolloff", (0, 50)),
            'Mag*Voice Slope': percentile_defaults.get("Mag*Voice Slope", (-1, 1)),
            'WOSS': percentile_defaults.get("WOSS", (-3, 3)),
            'hfc_percentile': 95, 'spec_decrease_percentile': 95,
            'colormap': 'rainbow', 'colormap_mag_voice': 'rainbow', 'colormap_dom_freq': 'rainbow', # Default back to rainbow
            'colormap_spec_decrease': 'rainbow', 'colormap_hfc': 'rainbow', 'colormap_bandwidth': 'rainbow',
            'colormap_rolloff': 'rainbow', 'colormap_woss': 'RdBu', 'section_colormap': 'RdBu',
            'colormap_mag_voice_slope': 'RdBu', # Keep RdBu for slopes/WOSS/Sections
            'batched_mode': True  # Flag to indicate we're using batched mode
        }

    return settings

def get_plot_settings(dt, header_loader, segy_path, is_2d_3d_mode=False):
    """
    Configure all plot settings including spectral analysis parameters and display options.

    This function orchestrates the complete configuration process for spectral analysis
    and visualization. It collects general settings, computes global normalization
    parameters from sample data, and configures display settings.

    Args:
        dt: Sampling interval in seconds
        header_loader: SegyHeaderLoader object containing survey geometry
        segy_path: Path to the SEG-Y file
        is_2d_3d_mode: Flag indicating if operating in 2D/3D mode (optional)

    Returns:
        dict: Comprehensive dictionary of all plot and analysis settings
    """
    # Get number of samples from SEG-Y file
    with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
        num_samples = segyfile.samples.size
        general_spectral_settings = get_general_and_spectral_settings(dt, num_samples)

    print("\nComputing global normalization parameters from random samples...")
    total_traces = len(header_loader.unique_indices)
    desired_samples = int((general_spectral_settings['sample_percent'] / 100.0) * total_traces)
    num_samples = min(max(1, desired_samples), general_spectral_settings['max_traces'])
    print(f"Sampling {general_spectral_settings['sample_percent']}% of {total_traces} traces, limited to {general_spectral_settings['max_traces']}: using {num_samples} samples")

    sample_indices = np.random.choice(header_loader.unique_indices, num_samples, replace=False)

    # Always use batched version for statistical analysis
    # Use the same fallback logic as above
    try:
        from .dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu_2d_chunked_mag as dlogst_spec_descriptor_batch
    except ImportError:
        try:
            from .dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu_2d_chunked_mag as dlogst_spec_descriptor_batch
            logging.info("Using CPU implementation for batch statistical analysis")
        except ImportError:
            def dlogst_spec_descriptor_batch(*args, **kwargs):
                raise NotImplementedError("Neither GPU nor CPU batch spectral descriptor functions are available.")

    # Load all traces
    trace_samples = []
    for trace_idx in tqdm(sample_indices, desc="Loading random traces"):
        trace_sample = load_trace_sample(segy_path, trace_idx)
        trace_samples.append(trace_sample)

    # Check for consistent trace lengths and pad if necessary
    trace_lengths = [len(t) for t in trace_samples]
    if min(trace_lengths) != max(trace_lengths):
        print("Traces have different lengths. Applying zero padding for batch processing...")
        max_len = max(trace_lengths)
        for i in range(len(trace_samples)):
            if len(trace_samples[i]) < max_len:
                trace_samples[i] = np.pad(trace_samples[i], (0, max_len - len(trace_samples[i])), mode='constant')

    # Stack traces into a batch
    trace_batch = np.stack(trace_samples)

    # Get suggested batch size using the helper function
    suggested_batch, free_mb = get_suggested_batch_size()

    print(f"Using batched processing with batch size {suggested_batch} for statistical analysis")

    # Process the batch
    descriptor_settings = {
        'use_band_limited': general_spectral_settings['use_band_limited'],
        'shape': general_spectral_settings['shape'],
        'kmax': general_spectral_settings['kmax'],
        'int_val': general_spectral_settings['int_val'],
        'b1': general_spectral_settings['b1'],
        'b2': general_spectral_settings['b2'],
        'p_bandwidth': general_spectral_settings.get('p_bandwidth', 2),
        'roll_percent': general_spectral_settings.get('roll_percent', 0.85)
    }

    try:
        batch_results = dlogst_spec_descriptor_batch(
            trace_batch, dt, fmax=trace_batch.shape[1]//2,
            batch_size=suggested_batch,
            **descriptor_settings
        )

        # Convert batch results to individual descriptors
        sample_descriptors = []
        for i in range(trace_batch.shape[0]):
            di = {}
            for key in batch_results:
                val = batch_results[key]
                if isinstance(val, np.ndarray) and val.shape[0] == trace_batch.shape[0]:
                    di[key] = val[i]
                else:
                    di[key] = val
            sample_descriptors.append(di)

    except Exception as e:
        print(f"Batched processing failed: {e}. Falling back to non-batched processing.")
        # Fall back to non-batched processing as a last resort
        sample_descriptors = []
        for trace_idx in tqdm(sample_indices, desc="Processing random traces"):
            trace_sample = load_trace_sample(segy_path, trace_idx)
            descriptor = dlogst_spec_descriptor(
                trace_sample, dt, fmax=len(trace_sample)//2,
                use_band_limited=general_spectral_settings['use_band_limited'],
                shape=general_spectral_settings['shape'],
                kmax=general_spectral_settings['kmax'], int_val=general_spectral_settings['int_val'],
                b1=general_spectral_settings['b1'], b2=general_spectral_settings['b2'],
                p_bandwidth=general_spectral_settings.get('p_bandwidth', 2), roll_percent=general_spectral_settings.get('roll_percent', 0.85)
            )
            sample_descriptors.append(descriptor)

    # Pass general_spectral_settings to get_statistics_percentiles_and_display_settings
    display_settings = get_statistics_percentiles_and_display_settings(sample_descriptors, general_spectral_settings)

    settings = {**general_spectral_settings, **display_settings}

    # Calculate normalization factors for HFC and spectral decrease
    all_hfc = np.concatenate([d['hfc'] for d in sample_descriptors])
    all_spec_decrease = np.concatenate([d['spec_decrease'] for d in sample_descriptors])
    settings['hfc_p95'] = np.percentile(all_hfc, settings['hfc_percentile']) if all_hfc.size > 0 else 1.0
    settings['spec_decrease_p95'] = np.percentile(all_spec_decrease, settings['spec_decrease_percentile']) if all_spec_decrease.size > 0 else 1.0

    # Set color limits for various descriptors from direct settings
    for descriptor in ['Input Signal', 'Magnitude * Voice', 'Spectral Slope',
                      'Spectral Bandwidth', 'Spectral Rolloff', 'Mag*Voice Slope', 'WOSS']:
        if descriptor in settings:
            # This is now a direct min/max tuple from the dialog
            continue
        else:
            # Set defaults for any missing descriptor
            if descriptor in ['Input Signal', 'Magnitude * Voice', 'Mag*Voice Slope', 'WOSS']:
                settings[descriptor] = (-1, 1)
            else:
                settings[descriptor] = (0, 1)

    # Compatibility settings for old references
    settings['Spectral Decrease'] = (0, 1)
    settings['HFC'] = (0, 1)
    settings['Dominant Frequency'] = (0, settings['Frequency'][1])

    return settings

def select_outputs(is_2d_3d_mode=False, root=None):
    """
    Display a dialog for selecting which outputs to include in the analysis.

    Args:
        is_2d_3d_mode: If True, restrict outputs to those compatible with 2D/3D visualization
        root: Optional Tkinter root window. If None, a new Tk instance will be created.

    Returns:
        list: List of selected output names
    """
    # If root is not provided, create a new Tk instance
    if root is None:
        root = tk.Tk()
        root.withdraw()

    outputs = [
        "Seismic Amplitude",
        "Time-Frequency Magnitude Spectrogram",
        "Magnitude * Voice Spectrogram",
        "Normalized dominant frequencies",
        "Magnitude * Voice Slope Attribute",
        "Normalized Spectral Decrease",
        "Normalized High Frequency Content",
        "Spectral bandwidth",
        "Spectral roll-off",
        "WOSS (Weighted Optimum Spectral Shape)"
    ]

    # Remove computationally intensive options for 2D/3D mode
    if is_2d_3d_mode:
        outputs.remove("Time-Frequency Magnitude Spectrogram")
        outputs.remove("Magnitude * Voice Spectrogram")
        info_msg = "Note: 'Time-Frequency Magnitude Spectrogram' and 'Magnitude * Voice Spectrogram' are disabled in 2D/3D mode for performance reasons"
    else:
        info_msg = "Select outputs to include:"

    selection_window = tk.Toplevel(root)
    selection_window.title("Select Outputs")
    selection_window.geometry("400x450")
    tk.Label(selection_window, text=info_msg, wraplength=350).pack(pady=5)
    frame = tk.Frame(selection_window)
    frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    scrollbar = tk.Scrollbar(frame, orient="vertical")
    listbox = tk.Listbox(frame, selectmode=tk.MULTIPLE, yscrollcommand=scrollbar.set, width=40, height=15)
    scrollbar.config(command=listbox.yview)
    scrollbar.pack(side="right", fill="y")
    listbox.pack(side="left", fill="both", expand=True)
    for output in outputs:
        listbox.insert(tk.END, output)
    # Default selection: Input Signal
    listbox.selection_set(0)

    selected_indices = []
    def on_ok():
        nonlocal selected_indices
        selected_indices = listbox.curselection()
        selection_window.destroy()
    tk.Button(selection_window, text="OK", command=on_ok).pack(pady=10)
    selection_window.wait_window()
    return [outputs[i] for i in selected_indices]

def analyze_and_plot_trace(trace_sample, trace_idx, dt, well_marker_name, plot_settings,
                           plot_marker=False, marker_value=None, hfc_normalized=None,
                           spec_decrease_normalized=None, descriptors=None, selected_outputs=None):
    if selected_outputs is None:
        selected_outputs = ["Seismic Amplitude"]  # Default to showing at least the input signal

    num_samples = len(trace_sample)
    time_vector = np.arange(num_samples) * dt

    if descriptors is None:
        descriptors = dlogst_spec_descriptor(
            trace_sample, dt, fmax=num_samples//2,
            use_band_limited=plot_settings['use_band_limited'],
            shape=plot_settings['shape'], kmax=plot_settings['kmax'],
            int_val=plot_settings['int_val'], b1=plot_settings['b1'], b2=plot_settings['b2'],
            p_bandwidth=plot_settings.get('p_bandwidth', 2), roll_percent=plot_settings.get('roll_percent', 0.85)
        )

    # Extract descriptors - only extract what we need and check if they exist
    # This is safer for batched mode where some keys might be missing
    freqst = descriptors.get('freqst', None)
    peak_freq = descriptors.get('peak_freq', None)
    spec_centroid = descriptors.get('spec_centroid', None)
    fdom = descriptors.get('fdom', None)
    spec_bandwidth = descriptors.get('spec_bandwidth', None)
    spec_rolloff = descriptors.get('spec_rolloff', None)
    mag_voice_slope = descriptors.get('mag_voice_slope', None)

    # Check if we're in batched mode
    batched_mode = plot_settings.get('batched_mode', False)

    if hfc_normalized is None or spec_decrease_normalized is None:
        raise ValueError("Global normalization for HFC and Spectral Decrease must be provided.")

    freq_limit_index = np.argmin(np.abs(freqst - plot_settings['Frequency'][1]))

    # Define subplot titles based on selected outputs with word wrapping
    subplot_titles = []
    for output in selected_outputs:
        if output == "Normalized Spectral Decrease":
            decrease_title = f"Normalized Spectral<br>Decrease<br>(Band [{plot_settings['b1']}-{plot_settings['b2']} Hz])" if plot_settings['use_band_limited'] else "Normalized<br>Spectral<br>Decrease<br>(Full-Band)"
            subplot_titles.append(decrease_title)
        else:
            subplot_titles.append(output.replace(" ", "<br>"))  # Wrap words with line breaks.

    fig = make_subplots(rows=1, cols=len(subplot_titles), subplot_titles=subplot_titles)

    # Define wider horizontal spacing
    colorbar_spacing = 0.025  # Increased from 0.01 to 0.025 for wider gaps
    initial_x = 1.02  # Increased from 1.005 to 1.02 for more space from plot edge

    # No need to calculate mag_voice here, it should be available directly in descriptors

    col_idx = 1
    for output in selected_outputs:
        if output == "Seismic Amplitude":
            fig.add_trace(go.Scatter(x=trace_sample, y=time_vector, mode='lines'), row=1, col=col_idx)
            fig.update_xaxes(title_text='Amplitude', row=1, col=col_idx,
                            range=plot_settings.get('Input Signal', [-1, 1]))
            col_idx += 1
        elif output == "Time-Frequency Magnitude Spectrogram":
            # Check if we're in batched mode
            batched_mode = plot_settings.get('batched_mode', False)

            # Check if mag is available in descriptors and we're not in batched mode
            if not batched_mode and 'mag' in descriptors and isinstance(descriptors['mag'], np.ndarray):
                mag = descriptors['mag']  # Get the mag array from descriptors
                fig.add_trace(go.Heatmap(
                    z=mag[:freq_limit_index, :].T,
                    x=freqst[:freq_limit_index],
                    y=time_vector,
                    colorscale=plot_settings['colormap'],
                    zmin=plot_settings.get("Magnitude Spectrogram", [None, None])[0], # Apply zmin from settings
                    zmax=plot_settings.get("Magnitude Spectrogram", [None, None])[1], # Apply zmax from settings
                    colorbar=dict(
                        title=dict(
                            text='Time-Frequency<br>Magnitude<br>Spectrogram',  # Wrapped colorbar title
                            side='right',
                            font=dict(size=12)  # Move titlefont into title dictionary as font
                        ),
                        x=initial_x + (col_idx - 2) * colorbar_spacing,
                        y=0.5,
                        len=0.4,
                        yanchor='middle',
                        xanchor='left',
                        tickfont=dict(size=10)
                    )
                ), row=1, col=col_idx)
                fig.update_xaxes(title_text='Frequency (Hz)', row=1, col=col_idx, range=plot_settings['Frequency'])
            else:
                # If mag is not available or we're in batched mode, display a message
                fig.add_annotation(
                    text="Time-Frequency Magnitude Spectrogram<br>not available in batched processing mode",
                    x=0.5, y=0.5,
                    xref=f"x{col_idx}", yref=f"y{col_idx}",
                    showarrow=False,
                    font=dict(size=12, color="red")
                )
                fig.update_xaxes(title_text='', row=1, col=col_idx)
            col_idx += 1
        elif output == "Magnitude * Voice Spectrogram":
            # Check if we're in batched mode
            batched_mode = plot_settings.get('batched_mode', False)

            # Check if mag_voice is available in descriptors and we're not in batched mode
            if not batched_mode and 'mag_voice' in descriptors and isinstance(descriptors['mag_voice'], np.ndarray):
                # Use mag_voice directly from descriptors
                fig.add_trace(go.Heatmap(
                    z=descriptors['mag_voice'][:freq_limit_index, :].T,
                    x=freqst[:freq_limit_index],
                    y=time_vector,
                    colorscale=plot_settings['colormap_mag_voice'],
                    zmin=plot_settings["Magnitude * Voice"][0],
                    zmax=plot_settings["Magnitude * Voice"][1],
                    colorbar=dict(
                        title=dict(
                            text='Magnitude<br>* Voice<br>Spectrogram',  # Wrapped colorbar title
                            side='right',
                            font=dict(size=12)  # Move titlefont into title dictionary as font
                        ),
                        x=initial_x + (col_idx - 2) * colorbar_spacing,
                        y=0.5,
                        len=0.4,
                        yanchor='middle',
                        xanchor='left',
                        tickfont=dict(size=10)
                    )
                ), row=1, col=col_idx)
                fig.update_xaxes(title_text='Frequency (Hz)', row=1, col=col_idx, range=plot_settings['Frequency'])
            else:
                # If mag_voice is not available or we're in batched mode, display a message
                fig.add_annotation(
                    text="Magnitude * Voice Spectrogram<br>not available in batched processing mode",
                    x=0.5, y=0.5,
                    xref=f"x{col_idx}", yref=f"y{col_idx}",
                    showarrow=False,
                    font=dict(size=12, color="red")
                )
                fig.update_xaxes(title_text='', row=1, col=col_idx)
            col_idx += 1
        elif output == "Normalized dominant frequencies":
            fig.add_trace(go.Scatter(x=peak_freq, y=time_vector, mode='lines', name='Peak Frequency'), row=1, col=col_idx)
            fig.add_trace(go.Scatter(x=spec_centroid, y=time_vector, mode='lines', name='Spectral Centroid'), row=1, col=col_idx)
            fig.add_trace(go.Scatter(x=fdom, y=time_vector, mode='lines', name='Dominant Frequency'), row=1, col=col_idx)
            fig.update_xaxes(title_text='Frequency (Hz)', row=1, col=col_idx, range=plot_settings['Frequency'])
            col_idx += 1
        elif output == "Magnitude * Voice Slope Attribute":
            fig.add_trace(go.Scatter(x=mag_voice_slope, y=time_vector, mode='lines'), row=1, col=col_idx)
            fig.update_xaxes(title_text='Slope', row=1, col=col_idx,
                            range=plot_settings.get('Mag*Voice Slope', [-1, 1]))
            col_idx += 1
        elif output == "Normalized Spectral Decrease":
            fig.add_trace(go.Scatter(x=spec_decrease_normalized, y=time_vector, mode='lines'), row=1, col=col_idx)
            fig.update_xaxes(title_text='Normalized Decrease', row=1, col=col_idx, range=plot_settings.get('Spectral Decrease', [0, 1]))
            col_idx += 1

        elif output == "Normalized High Frequency Content":
            fig.add_trace(go.Scatter(x=hfc_normalized, y=time_vector, mode='lines'), row=1, col=col_idx)
            fig.update_xaxes(title_text='Normalized Energy', row=1, col=col_idx, range=plot_settings.get('HFC', [0, 1]))
            col_idx += 1
        elif output == "Spectral bandwidth":
            fig.add_trace(go.Scatter(x=spec_bandwidth, y=time_vector, mode='lines', line=dict(color='green')), row=1, col=col_idx)
            fig.update_xaxes(title_text='Bandwidth (Hz)', row=1, col=col_idx, range=plot_settings.get('Spectral Bandwidth', [0, 50]))
            col_idx += 1
        elif output == "Spectral roll-off":
            fig.add_trace(go.Scatter(x=spec_rolloff, y=time_vector, mode='lines', line=dict(color='orange')), row=1, col=col_idx)
            fig.update_xaxes(title_text='Rolloff (Hz)', row=1, col=col_idx, range=plot_settings.get('Spectral Rolloff', [0, 50]))
            col_idx += 1
        elif output == "WOSS (Weighted Optimum Spectral Shape)":
            epsilon = plot_settings.get('epsilon', 1e-10)
            fdom_exponent = plot_settings.get('fdom_exponent', 2.0)
            # pull normalized dominant frequency from descriptors
            norm_fdom = descriptors.get("norm_fdom")
            denominator = hfc_normalized * (norm_fdom**fdom_exponent + epsilon)
            with np.errstate(divide='ignore', invalid='ignore'):
                woss = np.where(denominator > epsilon, mag_voice_slope / denominator, 0.0)
                woss[~np.isfinite(woss)] = 0.0
                woss = np.clip(woss, -1e6, 1e6)
            fig.add_trace(go.Scatter(x=woss, y=time_vector, mode='lines', line=dict(color='purple')), row=1, col=col_idx)
            fig.update_xaxes(title_text='WOSS Value', row=1, col=col_idx, range=plot_settings.get('WOSS', [-3, 3]))
            col_idx += 1

    if plot_marker and marker_value is not None:
        for i in range(1, len(subplot_titles) + 1):
            fig.add_hline(y=marker_value, line=dict(color='red', dash='dash'), row=1, col=i)

    for i in range(1, len(subplot_titles) + 1):
        fig.update_yaxes(title_text='Time (s)', row=1, col=i, range=plot_settings['Time (Y-axis)'][::-1])

    # Count the number of heatmap outputs that need colorbars
    heatmap_outputs = sum(1 for o in selected_outputs if o in ["Magnitude Spectrogram", "Magnitude * Voice"])

    fig.update_layout(
        title={
            'text': f"Spectral Descriptors for Trace from '{well_marker_name}'<br>(Trace Index {trace_idx}, fdom^{fdom_exponent:.1f})",
            'font': {'size': 15, 'family': 'Arial', 'color': 'black', 'weight': 'bold'},
            'x': 0,
            'y': 0.98,
            'xanchor': 'left',
            'yanchor': 'top',
        },
        height=850,  # Set to 850 for option 1, suboption 1
        width=max(800, 300 * len(subplot_titles) + 80 * heatmap_outputs),  # Width based on number of columns
        showlegend=False,
        margin=dict(r=70 + 80 * heatmap_outputs)  # Right margin for colorbars
    )
    fig.show()

def plot_seismic_section(trace_data, descriptors_list, dt, plot_settings, selected_outputs, header_loader, selection_mode, fixed_inline=None, fixed_crossline=None):
    """
    Plot seismic section with spectral attributes.

    This function creates a comprehensive visualization of seismic data and its
    spectral attributes. It can display multiple traces side-by-side with various
    spectral attributes including WOSS, dominant frequencies, spectral decrease, etc.

    Args:
        trace_data: List of dictionaries containing trace samples and metadata
        descriptors_list: List of dictionaries containing spectral descriptors for each trace
        dt: Sampling interval in seconds
        plot_settings: Dictionary of plot configuration parameters
        selected_outputs: List of attribute names to display
        header_loader: SegyHeaderLoader object containing survey geometry
        selection_mode: Integer indicating the trace selection mode used
        fixed_inline: Inline number for inline section display (optional)
        fixed_crossline: Crossline number for crossline section display (optional)

    Returns:
        None: Displays the plot using Plotly
    """
    # Calculate time vector based on trace sample length and sampling interval
    num_samples = len(trace_data[0]['trace_sample'])
    time_vector = np.arange(num_samples) * dt

    # Determine X-axis based on selection mode and 3D inline/crossline choice
    if selection_mode == 2 or (selection_mode == 6 and fixed_inline is not None):  # Specific inline or fixed inline in 3D
        x_axis = [header_loader.crosslines[np.where(header_loader.unique_indices == t['trace_idx'])[0][0]] for t in trace_data]
        x_label = "Crossline"
        title_suffix = f"Inline {fixed_inline}" if selection_mode == 6 else f"Inline {header_loader.inlines[np.where(header_loader.unique_indices == trace_data[0]['trace_idx'])[0][0]]}"
    elif selection_mode == 3 or (selection_mode == 6 and fixed_crossline is not None):  # Specific crossline or fixed crossline in 3D
        x_axis = [header_loader.inlines[np.where(header_loader.unique_indices == t['trace_idx'])[0][0]] for t in trace_data]
        x_label = "Inline"
        title_suffix = f"Crossline {fixed_crossline}" if selection_mode == 6 else f"Crossline {header_loader.crosslines[np.where(header_loader.unique_indices == trace_data[0]['trace_idx'])[0][0]]}"
    elif selection_mode == 4:  # Inline range
        x_axis = [header_loader.crosslines[np.where(header_loader.unique_indices == t['trace_idx'])[0][0]] for t in trace_data]
        x_label = "Crossline"
        title_suffix = f"Inline Range"
    elif selection_mode == 5:  # Polyline selection
        # For polyline selection, we'll use sequential numbering but add trace index labels
        x_axis = list(range(len(trace_data)))
        x_label = "Trace Position Along Polyline"
        title_suffix = f"Polyline Selection ({len(trace_data)} traces)"
    else:  # Default case for other selection modes (7 or any undefined mode)
        x_axis = list(range(len(trace_data)))  # Simple sequential numbering
        x_label = "Trace Index"
        title_suffix = "Selected Traces"

    # Prepare data matrices for each output with word-wrapped titles
    data_matrices = {}
    titles = {}
    unavailable_outputs = []

    # Check if we're in batched mode
    batched_mode = plot_settings.get('batched_mode', False)

    for output in selected_outputs:
        if output == "Seismic Amplitude":
            data_matrices[output] = np.array([t['trace_sample'] for t in trace_data]).T
            titles[output] = "Seismic<br>Amplitude"
        elif output == "Time-Frequency Magnitude Spectrogram":
            # In batched mode, Magnitude Spectrogram is not available
            if batched_mode or not any('mag' in d and isinstance(d['mag'], np.ndarray) for d in descriptors_list):
                unavailable_outputs.append(output)
                titles[output] = "Time-Frequency<br>Magnitude<br>Spectrogram"
            else:
                # This would be implemented if mag was available and we're not in batched mode
                unavailable_outputs.append(output)
                titles[output] = "Time-Frequency<br>Magnitude<br>Spectrogram"
        elif output == "Magnitude * Voice Spectrogram":
            # In batched mode, Magnitude * Voice is not available
            if batched_mode or not any('mag_voice' in d and isinstance(d['mag_voice'], np.ndarray) for d in descriptors_list):
                unavailable_outputs.append(output)
                titles[output] = "Magnitude<br>* Voice<br>Spectrogram"
            else:
                # This would be implemented if mag_voice was available and we're not in batched mode
                unavailable_outputs.append(output)
                titles[output] = "Magnitude<br>* Voice<br>Spectrogram"
        elif output == "Normalized dominant frequencies":
            data_matrices[output] = np.array([d['fdom'] for d in descriptors_list]).T  # Using fdom
            titles[output] = "Dominant<br>Frequency<br>(Hz)"
        elif output == "Magnitude * Voice Slope Attribute":
            data_matrices[output] = np.array([d['mag_voice_slope'] for d in descriptors_list]).T
            titles[output] = "Magnitude<br>* Voice<br>Slope"
        elif output == "Normalized Spectral Decrease":
            data_matrices[output] = np.array([d['spec_decrease'] / plot_settings['spec_decrease_p95'] for d in descriptors_list]).T
            titles[output] = f"Normalized<br>Spectral<br>Decrease<br>({'Band-Limited' if plot_settings['use_band_limited'] else 'Full-Band'})"
        elif output == "Normalized High Frequency Content":
            data_matrices[output] = np.array([d['hfc'] / plot_settings['hfc_p95'] for d in descriptors_list]).T
            titles[output] = "Normalized<br>HFC"
        elif output == "Spectral bandwidth":
            data_matrices[output] = np.array([d['spec_bandwidth'] for d in descriptors_list]).T
            titles[output] = "Spectral<br>Bandwidth<br>(Hz)"
        elif output == "Spectral roll-off":
            data_matrices[output] = np.array([d['spec_rolloff'] for d in descriptors_list]).T
            titles[output] = "Spectral<br>Rolloff<br>(Hz)"
        elif output == "WOSS (Weighted Optimum Spectral Shape)":
            epsilon = plot_settings.get('epsilon', 1e-10)
            fdom_exponent = plot_settings.get('fdom_exponent', 2.0)
            hfc_norm = np.array([d['hfc'] / plot_settings['hfc_p95'] for d in descriptors_list])
            norm_fdom = np.array([d['norm_fdom'] for d in descriptors_list])
            mag_voice_slope = np.array([d['mag_voice_slope'] for d in descriptors_list])
            denominator = hfc_norm * (norm_fdom**fdom_exponent + epsilon)
            # Apply a threshold to avoid division by very small values
            safe_denominator = np.where(np.abs(denominator) > epsilon, denominator, epsilon)
            # Now perform the division with the safe denominator
            with np.errstate(divide='ignore', invalid='ignore'):
                woss = np.where(
                    denominator > epsilon,
                    mag_voice_slope / safe_denominator,
                    0.0
                )
                woss[~np.isfinite(woss)] = 0.0
                woss = np.clip(woss, -1e6, 1e6)
            data_matrices[output] = woss.T
            titles[output] = f"WOSS<br>(fdom^{fdom_exponent:.1f})"

    # Create subplots
    num_outputs = len(selected_outputs)

    # Determine layout based on number of outputs
    if num_outputs <= 4:
        num_rows = 1
        num_cols = num_outputs
        vertical_spacing = None
        top_margin = 50  # default top margin
    else:
        num_rows = 2
        num_cols = min(4, (num_outputs + 1) // 2)
        vertical_spacing = 0.13  # moderate vertical spacing
        top_margin = 80  # moderate top margin

    # Create subplot titles in row-wise order
    subplot_titles = [titles[output] for output in selected_outputs]

    fig = make_subplots(rows=num_rows, cols=num_cols,
                        subplot_titles=subplot_titles,
                        vertical_spacing=vertical_spacing)

    # Define colorbar positioning parameters based on the new layout
    colorbar_spacing = 0.055  # Increased from 0.05 to widen horizontal gap between colorbars by ~10%
    initial_x = 1.02  # Starting position for the first colorbar

    # Define colormap mapping for each output
    colormap_mapping = {
        "Seismic Amplitude": plot_settings.get('section_colormap', 'viridis'),
        "Normalized dominant frequencies": plot_settings.get('colormap_dom_freq', 'rainbow'),
        "Magnitude * Voice Slope Attribute": plot_settings.get('colormap_mag_voice', 'plasma'),
        "Normalized Spectral Decrease": plot_settings.get('colormap_spec_decrease', 'rainbow'),
        "Normalized High Frequency Content": plot_settings.get('colormap_hfc', 'rainbow'),
        "Spectral bandwidth": plot_settings.get('colormap_bandwidth', 'rainbow'),
        "Spectral roll-off": plot_settings.get('colormap_rolloff', 'rainbow'),
        "WOSS (Weighted Optimum Spectral Shape)": plot_settings.get('colormap_woss', 'RdBu')
    }

    # Define colorscale ranges for each output using custom min/max values from plot_settings
    colorscale_ranges = {
        "Seismic Amplitude": [plot_settings.get('input_signal_min', -1), plot_settings.get('input_signal_max', 1)],
        "Normalized dominant frequencies": plot_settings.get('Dominant Frequency', [0, 100]),
        "Magnitude * Voice Slope Attribute": plot_settings.get('Mag*Voice Slope', [-1, 1]),
        "Normalized Spectral Decrease": [plot_settings.get('spec_decrease_min', 0), plot_settings.get('spec_decrease_max', 1)],
        "Normalized High Frequency Content": [plot_settings.get('hfc_min', 0), plot_settings.get('hfc_max', 1)],
        "Spectral bandwidth": plot_settings.get('Spectral Bandwidth', [0, 50]),
        "Spectral roll-off": plot_settings.get('Spectral Rolloff', [0, 50]),
        "WOSS (Weighted Optimum Spectral Shape)": [plot_settings.get('woss_min', -3), plot_settings.get('woss_max', 3)]
    }

    idx = 0
    for output in selected_outputs:
        # Calculate the row and column for each subplot
        if num_rows == 1:
            # Single row layout
            row = 1
            col = idx % num_cols + 1
        else:
            # Two-row layout
            row = idx // num_cols + 1
            col = idx % num_cols + 1

        # Skip if we've run out of grid positions
        if row > num_rows or col > num_cols:
            print(f"Warning: Not enough subplot positions for output {output}. Skipping.")
            continue

        # Handle unavailable outputs (like Magnitude * Voice in batched mode)
        if output in unavailable_outputs:
            # Add an empty trace to ensure the subplot is created
            fig.add_trace(go.Scatter(x=[None], y=[None], mode='lines', visible=False), row=row, col=col)
            # Add an annotation explaining why it's not available
            fig.add_annotation(
                text=f"{output} not available<br>in batched processing mode",
                x=0.5, y=0.5,
                xref=f"x{col}", yref=f"y{col}",
                showarrow=False,
                font=dict(size=12, color="red")
            )
            fig.update_xaxes(title_text=x_label, row=row, col=col)
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
            idx += 1
            continue

        # Get the appropriate colorscale for this output
        colorscale = colormap_mapping.get(output, 'viridis')

        # Get the colorscale range for this output
        zmin, zmax = colorscale_ranges.get(output, [None, None])

        colorbar_title = output.split('<br>')[0]  # Use first line for colorbar title, wrapped if needed
        if '<br>' in output:
            colorbar_title = '<br>'.join(output.split('<br>')[:2])  # Take first two lines for longer titles

        # Use idx instead of col to ensure unique x positions for each colorbar
        colorbar_x = initial_x + idx * colorbar_spacing

        fig.add_trace(
            go.Heatmap(
                z=data_matrices[output],
                x=x_axis,
                y=time_vector,
                colorscale=colorscale,  # Use the mapped colorscale
                zmin=zmin,  # Apply min value for color scale
                zmax=zmax,  # Apply max value for color scale
                colorbar=dict(
                    title=dict(
                        text=colorbar_title,  # Wrapped colorbar title
                        side='right',
                        font=dict(size=12)
                    ),
                    x=colorbar_x,
                    y=0.5,
                    len=0.8,  # Slightly longer for seismic sections
                    yanchor='middle',
                    xanchor='left',
                    tickfont=dict(size=10)
                )
            ),
            row=row, col=col
        )
        fig.update_xaxes(title_text=x_label, row=row, col=col)
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])

        idx += 1

    section_type = "2D" if selection_mode in [2, 3, 4, 5] else "3D"

    # Calculate appropriate height based on number of rows and section type
    if num_rows == 1:
        figure_height = 900  # Increased from 700 to make single-row subplots taller
    else:
        # Increase height for multi-row layouts to allocate more height to subplots
        figure_height = 1200  # Increased from 980 to make two-row subplots taller

    fig.update_layout(
        title=dict(
            text=f"{section_type} Seismic Section Analysis - {title_suffix}",
            y=0.98,
            x=0,
            xanchor='left',
            yanchor='top',
            font=dict(size=15, family='Arial', color='black', weight='bold')
        ),
        height=figure_height,
        width=max(800, 450 * num_cols + 80 * num_cols),
        showlegend=False,
        margin=dict(r=70 + 80 * num_outputs, t=top_margin)
    )
    # Set subplot (spectral descriptor) title font size to 17
    for annotation in fig['layout']['annotations']:
        annotation['font'] = dict(size=15, family='Arial', color='black', weight='bold')
    fig.show()

def get_range_input(title="Input Range", initial_values=(100, 150, 200, 250), root=None):
    """
    Create a custom dialog with separate inputs for inline and crossline ranges.

    Args:
        title: Dialog window title
        initial_values: Tuple of (min_inline, max_inline, min_crossline, max_crossline)
        root: Optional Tkinter root window. If None, a new Tk instance will be created.

    Returns:
        Tuple of (min_inline, max_inline, min_crossline, max_crossline) or None if canceled
    """
    # If root is not provided, create a new Tk instance
    if root is None:
        root = tk.Tk()
        root.withdraw()

    dialog = tk.Toplevel(root)
    dialog.title(title)
    dialog.grab_set()  # Make dialog modal

    # Set dialog position to center of screen
    dialog.geometry("350x170")
    dialog.resizable(False, False)

    # Create a frame for the form
    form_frame = tk.Frame(dialog, padx=20, pady=10)
    form_frame.pack(fill=tk.BOTH, expand=True)

    # Create input fields with labels
    tk.Label(form_frame, text="Inline Range:").grid(row=0, column=0, sticky="w", pady=(5, 0))

    inline_frame = tk.Frame(form_frame)
    inline_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
    tk.Label(inline_frame, text="Min:").pack(side=tk.LEFT, padx=(0, 5))
    min_inline_entry = tk.Entry(inline_frame, width=8)
    min_inline_entry.pack(side=tk.LEFT, padx=(0, 10))
    min_inline_entry.insert(0, str(initial_values[0]))

    tk.Label(inline_frame, text="Max:").pack(side=tk.LEFT, padx=(0, 5))
    max_inline_entry = tk.Entry(inline_frame, width=8)
    max_inline_entry.pack(side=tk.LEFT)
    max_inline_entry.insert(0, str(initial_values[1]))

    tk.Label(form_frame, text="Crossline Range:").grid(row=2, column=0, sticky="w", pady=(5, 0))

    crossline_frame = tk.Frame(form_frame)
    crossline_frame.grid(row=3, column=0, sticky="ew", pady=(0, 10))

    tk.Label(crossline_frame, text="Min:").pack(side=tk.LEFT, padx=(0, 5))
    min_crossline_entry = tk.Entry(crossline_frame, width=8)
    min_crossline_entry.pack(side=tk.LEFT, padx=(0, 10))
    min_crossline_entry.insert(0, str(initial_values[2]))

    tk.Label(crossline_frame, text="Max:").pack(side=tk.LEFT, padx=(0, 5))
    max_crossline_entry = tk.Entry(crossline_frame, width=8)
    max_crossline_entry.pack(side=tk.LEFT)
    max_crossline_entry.insert(0, str(initial_values[3]))

    # Create button frame
    button_frame = tk.Frame(dialog)
    button_frame.pack(fill=tk.X, padx=20, pady=(0, 10))

    result = [None]  # Using a list to store the result since nonlocal is not needed for lists

    def on_ok():
        try:
            result[0] = (
                int(min_inline_entry.get()),
                int(max_inline_entry.get()),
                int(min_crossline_entry.get()),
                int(max_crossline_entry.get())
            )
            dialog.destroy()
        except ValueError:
            messagebox.showerror("Error", "Please enter valid integer values for all fields.", parent=dialog)

    def on_cancel():
        dialog.destroy()

    tk.Button(button_frame, text="OK", command=on_ok, width=10).pack(side=tk.LEFT, padx=(0, 10))
    tk.Button(button_frame, text="Cancel", width=10, command=on_cancel).pack(side=tk.LEFT)

    # Wait for the dialog to be closed
    dialog.wait_window()
    return result[0]

def get_3d_section_selection(min_inline, max_inline, min_crossline, max_crossline, root=None):
    """
    Create a custom dialog to select 3D section display type and specific inline/crossline number.

    Args:
        min_inline, max_inline: Range of inline values
        min_crossline, max_crossline: Range of crossline values
        root: Optional Tkinter root window. If None, a new Tk instance will be created.

    Returns:
        Tuple of (line_choice, fixed_value) where:
        - line_choice: 1 for Specific Inline, 2 for Specific Crossline, or None if canceled
        - fixed_value: Selected inline or crossline number, or None if canceled
    """
    # If root is not provided, create a new Tk instance
    if root is None:
        root = tk.Tk()
        root.withdraw()

    dialog = tk.Toplevel(root)
    dialog.title("3D Section Selection")
    dialog.grab_set()  # Make dialog modal
    dialog.geometry("350x150")
    dialog.resizable(False, False)

    # Create a frame for the form
    form_frame = tk.Frame(dialog, padx=20, pady=10)
    form_frame.pack(fill=tk.BOTH, expand=True)

    # Display type selection
    tk.Label(form_frame, text=f"Select display type for IL {min_inline}-{max_inline}, XL {min_crossline}-{max_crossline}:").pack(anchor="w", pady=(5, 0))
    display_type_var = tk.StringVar(value="Inline")
    display_frame = tk.Frame(form_frame)
    display_frame.pack(fill=tk.X, pady=(5, 10))

    tk.Radiobutton(display_frame, text="Specific Inline", variable=display_type_var, value="Inline").pack(side=tk.LEFT, padx=(0, 20))
    tk.Radiobutton(display_frame, text="Specific Crossline", variable=display_type_var, value="Crossline").pack(side=tk.LEFT)

    # Number input
    number_frame = tk.Frame(form_frame)
    number_frame.pack(fill=tk.X, pady=(0, 10))

    number_label = tk.Label(number_frame, text="Enter Inline Number:")
    number_label.pack(side=tk.LEFT, padx=(0, 5))
    number_entry = tk.Entry(number_frame, width=10)
    number_entry.pack(side=tk.LEFT)
    number_entry.insert(0, str(min_inline))

    # Update label and entry based on selection
    # The *args parameter is required by trace_add but not used directly
    def update_label(*args):
        # args contains the name, mode, and other info from the trace_add callback
        # but we don't need to use it directly
        if display_type_var.get() == "Inline":
            number_label.config(text="Enter Inline Number:")
            number_entry.delete(0, tk.END)
            number_entry.insert(0, str(min_inline))
        else:
            number_label.config(text="Enter Crossline Number:")
            number_entry.delete(0, tk.END)
            number_entry.insert(0, str(min_crossline))

    display_type_var.trace_add("write", update_label)

    # Buttons
    button_frame = tk.Frame(dialog)
    button_frame.pack(fill=tk.X, padx=20, pady=(0, 10))

    result = [None, None]  # [line_choice, fixed_value]

    def on_ok():
        try:
            value = int(number_entry.get())
            if display_type_var.get() == "Inline":
                if not (min_inline <= value <= max_inline):
                    messagebox.showerror("Error", f"Inline number must be between {min_inline} and {max_inline}.", parent=dialog)
                    return
                result[0] = 1  # Specific Inline
                result[1] = value
            else:
                if not (min_crossline <= value <= max_crossline):
                    messagebox.showerror("Error", f"Crossline number must be between {min_crossline} and {max_crossline}.", parent=dialog)
                    return
                result[0] = 2  # Specific Crossline
                result[1] = value
            dialog.destroy()
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid integer.", parent=dialog)

    def on_cancel():
        dialog.destroy()

    tk.Button(button_frame, text="OK", command=on_ok, width=10).pack(side=tk.LEFT, padx=(0, 10))
    tk.Button(button_frame, text="Cancel", width=10, command=on_cancel).pack(side=tk.RIGHT)

    dialog.protocol("WM_DELETE_WINDOW", on_cancel)  # Handle window close

    # Center dialog on screen
    dialog.update_idletasks()
    width = dialog.winfo_width()
    height = dialog.winfo_height()
    screen_width = dialog.winfo_screenwidth()
    screen_height = dialog.winfo_screenheight()
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    dialog.geometry(f"{width}x{height}+{x}+{y}")

    dialog.wait_window()
    return tuple(result)

# Add this new helper function after other plotting functions

def add_output_to_subplot(fig, output, trace_data_item, descriptors, time_vector, row, col, plot_settings, hfc_p95, spec_decrease_p95, global_vmin=None, global_vmax=None, use_shared_colorbar=False, is_last_subplot=False):
    """Helper function to add a specific output to a subplot.

    This function handles adding different types of outputs (seismic amplitude, spectrograms, etc.)
    to a subplot in a figure. It applies the time and frequency limits from plot_settings.

    Args:
        fig: Plotly figure object to add the subplot to
        output: String indicating which output to add
        trace_data_item: Dictionary containing trace data
        descriptors: Dictionary containing spectral descriptors
        time_vector: Time vector for the y-axis
        row: Row index for the subplot
        col: Column index for the subplot
        plot_settings: Dictionary of plot settings
        hfc_p95: 95th percentile of HFC for normalization
        spec_decrease_p95: 95th percentile of spectral decrease for normalization
        global_vmin: Global minimum value for colormap (used for shared colormaps)
        global_vmax: Global maximum value for colormap (used for shared colormaps)
        use_shared_colorbar: If True, only show colorbar on the last subplot
        is_last_subplot: If True, this is the last subplot in the row (used for shared colorbar)
    """
    # Get time and frequency limits from plot settings
    # No need to create local variables since the code already uses plot_settings directly
    if output == "Seismic Amplitude" or output == "Input Signal":
        fig.add_trace(go.Scatter(x=trace_data_item['trace_sample'], y=time_vector, mode='lines'), row=row, col=col)
        # Use the cmap_min/cmap_max values from Step 2 if available
        min_val = plot_settings.get('input_signal_cmap_min', plot_settings.get('input_signal_min', -1))
        max_val = plot_settings.get('input_signal_cmap_max', plot_settings.get('input_signal_max', 1))
        fig.update_xaxes(title_text='Amplitude', row=row, col=col, range=[min_val, max_val])
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])

    elif output == "Time-Frequency Magnitude Spectrogram" or output == "Magnitude Spectrogram" or output == "Time-Frequency Magnitude Spectrogram":
        # Check if we're in batched mode
        batched_mode = plot_settings.get('batched_mode', False)

        # Check if mag is available in descriptors and we're not in batched mode
        if not batched_mode and 'mag' in descriptors and isinstance(descriptors['mag'], np.ndarray):
            mag = descriptors['mag']
            freqst = descriptors['freqst']

            # Use frequency limits from Step 2
            freq_min = plot_settings.get('Frequency', [0, 100])[0]
            freq_max = plot_settings.get('Frequency', [0, 100])[1]

            # Find the index corresponding to the max frequency
            freq_limit_index = np.argmin(np.abs(freqst - freq_max)) if len(freqst) > 0 else len(freqst)

            # Get colormap settings from Step 2
            key_base = "magnitude_spectrogram"
            cmap_name = plot_settings.get(f"{key_base}_cmap_name", 'rainbow')

            # Use global min/max values if provided, otherwise use local settings
            if global_vmin is not None and global_vmax is not None:
                cmap_min = global_vmin
                cmap_max = global_vmax
                logging.info(f"Using global min/max for Magnitude Spectrogram: {cmap_min}, {cmap_max}")
            else:
                cmap_min = plot_settings.get(f"{key_base}_cmap_min", 0)
                cmap_max = plot_settings.get(f"{key_base}_cmap_max", 1)

            # Use the colormap from settings if available
            colorscale = plot_settings.get('colormap', plot_settings.get('magnitude_spectrogram_colormap', cmap_name))

            # Determine whether to show colorbar based on shared colorbar setting
            show_colorbar = not use_shared_colorbar or (use_shared_colorbar and is_last_subplot)
            # Remove shared colorbar for Magnitude Spectrogram in comparative mode
            if use_shared_colorbar:
                show_colorbar = False

            # Create heatmap trace with or without colorbar
            heatmap_args = {
                'z': mag[:freq_limit_index, :].T,
                'x': freqst[:freq_limit_index],
                'y': time_vector,
                'colorscale': colorscale,
                'zmin': cmap_min,
                'zmax': cmap_max,
            }

            # Only add colorbar if we're showing it
            if show_colorbar:
                heatmap_args['colorbar'] = dict(
                    title=dict(
                        text='Time-Frequency<br>Magnitude<br>Spectrogram',  # Wrapped colorbar title
                        side='right',
                        font=dict(size=12)  # Move titlefont into title dictionary as font
                    ),
                    x=1.15,  # Fixed position for shared colorbar
                    y=0.5,
                    len=0.5,  # Make the colorbar slightly longer for better visibility
                    yanchor='middle',
                    xanchor='left',
                    tickfont=dict(size=10)
                )

            fig.add_trace(go.Heatmap(**heatmap_args), row=row, col=col)
            # Use frequency range from Step 2 for x-axis
            fig.update_xaxes(title_text='Frequency (Hz)', row=row, col=col, range=[freq_min, freq_max])
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
        else:
            # If mag is not available or we're in batched mode, display a message
            fig.add_annotation(
                text="Time-Frequency Magnitude Spectrogram<br>not available in batched processing mode",
                x=0.5, y=0.5,
                xref=f"x{col}", yref=f"y{col}",
                showarrow=False,
                font=dict(size=12, color="red")
            )
            # Add an empty trace to ensure the subplot is created
            fig.add_trace(go.Scatter(x=[None], y=[None], mode='lines', visible=False), row=row, col=col)
            # Use frequency range from Step 2 for x-axis
            freq_min = plot_settings.get('Frequency', [0, 100])[0]
            freq_max = plot_settings.get('Frequency', [0, 100])[1]
            fig.update_xaxes(title_text='Frequency (Hz)', row=row, col=col, range=[freq_min, freq_max])
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Magnitude * Voice Spectrogram" or output == "Magnitude * Voice" or output == "Magnitude*Voice":
        # Check if we're in batched mode
        batched_mode = plot_settings.get('batched_mode', False)

        # Check if mag_voice is available in descriptors and we're not in batched mode
        if not batched_mode and 'mag_voice' in descriptors and isinstance(descriptors['mag_voice'], np.ndarray):
            mag_voice = descriptors['mag_voice']
            freqst = descriptors['freqst']

            # Use frequency limits from Step 2
            freq_min = plot_settings.get('Frequency', [0, 100])[0]
            freq_max = plot_settings.get('Frequency', [0, 100])[1]

            # Find the index corresponding to the max frequency
            freq_limit_index = np.argmin(np.abs(freqst - freq_max)) if len(freqst) > 0 else len(freqst)

            # Get colormap settings from Step 2
            key_base = "magnitude__voice"
            cmap_name = plot_settings.get(f"{key_base}_cmap_name", 'rainbow')

            # Use global min/max values if provided, otherwise use local settings
            if global_vmin is not None and global_vmax is not None:
                cmap_min = global_vmin
                cmap_max = global_vmax
                logging.info(f"Using global min/max for Magnitude * Voice: {cmap_min}, {cmap_max}")
            else:
                cmap_min = plot_settings.get(f"{key_base}_cmap_min", -1)
                cmap_max = plot_settings.get(f"{key_base}_cmap_max", 1)

            # Use the colormap from settings if available
            colorscale = plot_settings.get('colormap_mag_voice', plot_settings.get('magnitude_voice_colormap', cmap_name))

            # Determine whether to show colorbar based on shared colorbar setting
            show_colorbar = not use_shared_colorbar or (use_shared_colorbar and is_last_subplot)
            # Remove shared colorbar for Magnitude * Voice Spectrogram in comparative mode
            if use_shared_colorbar:
                show_colorbar = False

            # Create heatmap trace with or without colorbar
            heatmap_args = {
                'z': mag_voice[:freq_limit_index, :].T,
                'x': freqst[:freq_limit_index],
                'y': time_vector,
                'colorscale': colorscale,
                'zmin': cmap_min,
                'zmax': cmap_max,
            }

            # Only add colorbar if we're showing it
            if show_colorbar:
                heatmap_args['colorbar'] = dict(
                    title=dict(
                        text='Magnitude<br>* Voice<br>Spectrogram',  # Wrapped colorbar title
                        side='right',
                        font=dict(size=12)  # Move titlefont into title dictionary as font
                    ),
                    x=1.20,  # Fixed position for shared colorbar for Magnitude * Voice (slightly to the right of Magnitude Spectrogram)
                    y=0.5,
                    len=0.5,  # Make the colorbar slightly longer for better visibility
                    yanchor='middle',
                    xanchor='left',
                    tickfont=dict(size=10)
                )

            fig.add_trace(go.Heatmap(**heatmap_args), row=row, col=col)
            # Use frequency range from Step 2 for x-axis
            fig.update_xaxes(title_text='Frequency (Hz)', row=row, col=col, range=[freq_min, freq_max])
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
        else:
            # If mag_voice is not available or we're in batched mode, display a message
            fig.add_annotation(
                text="Magnitude * Voice Spectrogram<br>not available in batched processing mode",
                x=0.5, y=0.5,
                xref=f"x{col}", yref=f"y{col}",
                showarrow=False,
                font=dict(size=12, color="red")
            )
            # Add an empty trace to ensure the subplot is created
            fig.add_trace(go.Scatter(x=[None], y=[None], mode='lines', visible=False), row=row, col=col)
            # Use frequency range from Step 2 for x-axis
            freq_min = plot_settings.get('Frequency', [0, 100])[0]
            freq_max = plot_settings.get('Frequency', [0, 100])[1]
            fig.update_xaxes(title_text='Frequency (Hz)', row=row, col=col, range=[freq_min, freq_max])
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Normalized dominant frequencies":
        peak_freq = descriptors['peak_freq']
        spec_centroid = descriptors['spec_centroid']
        fdom = descriptors['fdom']
        fig.add_trace(go.Scatter(x=peak_freq, y=time_vector, mode='lines', name='Peak Frequency'), row=row, col=col)
        fig.add_trace(go.Scatter(x=spec_centroid, y=time_vector, mode='lines', name='Spectral Centroid'), row=row, col=col)
        fig.add_trace(go.Scatter(x=fdom, y=time_vector, mode='lines', name='Dominant Frequency'), row=row, col=col)

        # Use frequency range from Step 2 for x-axis
        # First check if 'Frequency' key exists (which is the tuple created from freq_min and freq_max)
        if 'Frequency' in plot_settings:
            freq_range = plot_settings['Frequency']
        else:
            # If 'Frequency' doesn't exist, try to use freq_min and freq_max directly
            freq_min = plot_settings.get('freq_min', 0.0)
            freq_max = plot_settings.get('freq_max', 100.0)
            freq_range = [freq_min, freq_max]

        fig.update_xaxes(title_text='Frequency (Hz)', row=row, col=col, range=freq_range)
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Magnitude * Voice Slope Attribute" or output == "Mag*Voice Slope":
        mag_voice_slope = descriptors['mag_voice_slope']
        fig.add_trace(go.Scatter(x=mag_voice_slope, y=time_vector, mode='lines'), row=row, col=col)

        # Enhanced logging to debug the issue
        logging.info(f"Processing Mag*Voice Slope in add_output_to_subplot")
        logging.info(f"Available keys in plot_settings: {list(plot_settings.keys())}")

        # Try multiple possible key formats for Mag*Voice Slope limits
        if 'Mag*Voice Slope' in plot_settings and isinstance(plot_settings['Mag*Voice Slope'], list) and len(plot_settings['Mag*Voice Slope']) == 2:
            # Use the range passed as a list under 'Mag*Voice Slope' key
            min_val, max_val = plot_settings['Mag*Voice Slope']
            logging.info(f"Using Mag*Voice Slope limits from 'Mag*Voice Slope' key: {[min_val, max_val]}")
        elif 'mag_voice_slope' in plot_settings and isinstance(plot_settings['mag_voice_slope'], list) and len(plot_settings['mag_voice_slope']) == 2:
            # Use the range passed as a list under 'mag_voice_slope' key
            min_val, max_val = plot_settings['mag_voice_slope']
            logging.info(f"Using Mag*Voice Slope limits from 'mag_voice_slope' key: {[min_val, max_val]}")
        elif 'mag_voice_slope_cmap_min' in plot_settings and 'mag_voice_slope_cmap_max' in plot_settings:
            # Use individual min/max values
            min_val = plot_settings['mag_voice_slope_cmap_min']
            max_val = plot_settings['mag_voice_slope_cmap_max']
            logging.info(f"Using Mag*Voice Slope limits from individual min/max keys: {[min_val, max_val]}")
        else:
            # Calculate p5 and p95 from the current data if available
            try:
                p5 = np.percentile(mag_voice_slope, 5)
                p95 = np.percentile(mag_voice_slope, 95)
                # Use symmetric limits based on the larger absolute value
                abs_max = max(abs(p5), abs(p95))
                min_val = -abs_max
                max_val = abs_max
                logging.info(f"Using Mag*Voice Slope limits calculated from current data: {[min_val, max_val]}")
            except Exception as e:
                # Final fallback to reasonable defaults
                min_val = -10
                max_val = 10
                logging.info(f"Using default Mag*Voice Slope limits due to error: {e}")

        # Apply the limits to the plot
        fig.update_xaxes(title_text='Slope', row=row, col=col, range=[min_val, max_val])
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Spectral Slope":
        # Check if spec_slope exists in descriptors
        if 'spec_slope' in descriptors and isinstance(descriptors['spec_slope'], np.ndarray):
            spec_slope = descriptors['spec_slope']
            fig.add_trace(go.Scatter(x=spec_slope, y=time_vector, mode='lines'), row=row, col=col)

            # First check if the limits are passed as a list in 'Spectral Slope' key
            # This is the key set in app.py for individual plots
            if 'Spectral Slope' in plot_settings and isinstance(plot_settings['Spectral Slope'], list) and len(plot_settings['Spectral Slope']) == 2:
                # Use the range passed from app.py
                min_val, max_val = plot_settings['Spectral Slope']
            else:
                # Fallback to individual cmap_min/cmap_max values
                min_val = plot_settings.get('spectral_slope_cmap_min', plot_settings.get('spectral_slope_min', -1))
                max_val = plot_settings.get('spectral_slope_cmap_max', plot_settings.get('spectral_slope_max', 1))

            # Apply the limits to the plot
            fig.update_xaxes(title_text='Slope', row=row, col=col, range=[min_val, max_val])
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
        else:
            # If spec_slope is not available, display a message
            fig.add_annotation(
                text="Spectral Slope data not available",
                x=0.5, y=0.5,
                xref=f"x{col}", yref=f"y{col}",
                showarrow=False,
                font=dict(size=12, color="red")
            )
            # Add an empty trace to ensure the subplot is created
            fig.add_trace(go.Scatter(x=[None], y=[None], mode='lines', visible=False), row=row, col=col)
            fig.update_xaxes(title_text='Slope', row=row, col=col)
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Normalized Spectral Decrease" or output == "Spectral Decrease":
        spec_decrease_normalized = descriptors['spec_decrease'] / spec_decrease_p95 if spec_decrease_p95 != 0 else descriptors['spec_decrease']
        fig.add_trace(go.Scatter(x=spec_decrease_normalized, y=time_vector, mode='lines'), row=row, col=col)
        # Use the cmap_min/cmap_max values from Step 2 if available
        min_val = plot_settings.get('spectral_decrease_cmap_min', plot_settings.get('spec_decrease_min', 0))
        max_val = plot_settings.get('spectral_decrease_cmap_max', plot_settings.get('spec_decrease_max', 1))
        fig.update_xaxes(title_text='Normalized Decrease', row=row, col=col, range=[min_val, max_val])
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Normalized High Frequency Content" or output == "HFC":
        hfc_normalized = descriptors['hfc'] / hfc_p95 if hfc_p95 != 0 else descriptors['hfc']
        fig.add_trace(go.Scatter(x=hfc_normalized, y=time_vector, mode='lines'), row=row, col=col)
        # Use the cmap_min/cmap_max values from Step 2 if available
        min_val = plot_settings.get('hfc_cmap_min', plot_settings.get('hfc_min', 0))
        max_val = plot_settings.get('hfc_cmap_max', plot_settings.get('hfc_max', 1))
        fig.update_xaxes(title_text='Normalized Energy', row=row, col=col, range=[min_val, max_val])
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Spectral bandwidth" or output == "Spectral Bandwidth":
        spec_bandwidth = descriptors['spec_bandwidth']
        fig.add_trace(go.Scatter(x=spec_bandwidth, y=time_vector, mode='lines', line=dict(color='green')), row=row, col=col)
        # Use the cmap_min/cmap_max values from Step 2 if available
        min_val = plot_settings.get('spectral_bandwidth_cmap_min', plot_settings.get('spectral_bandwidth_min', 0))
        max_val = plot_settings.get('spectral_bandwidth_cmap_max', plot_settings.get('spectral_bandwidth_max', 50))
        fig.update_xaxes(title_text='Bandwidth (Hz)', row=row, col=col, range=[min_val, max_val])
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Spectral roll-off" or output == "Spectral Rolloff":
        spec_rolloff = descriptors['spec_rolloff']
        fig.add_trace(go.Scatter(x=spec_rolloff, y=time_vector, mode='lines', line=dict(color='orange')), row=row, col=col)
        # Use the cmap_min/cmap_max values from Step 2 if available
        min_val = plot_settings.get('spectral_rolloff_cmap_min', plot_settings.get('spectral_rolloff_min', 0))
        max_val = plot_settings.get('spectral_rolloff_cmap_max', plot_settings.get('spectral_rolloff_max', 50))
        fig.update_xaxes(title_text='Rolloff (Hz)', row=row, col=col, range=[min_val, max_val])
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "WOSS (Weighted Optimum Spectral Shape)" or output == "WOSS":
        epsilon = plot_settings.get('epsilon', 1e-10)
        fdom_exponent = plot_settings.get('fdom_exponent', 2.0)
        hfc_normalized = descriptors['hfc'] / hfc_p95
        norm_fdom = descriptors['norm_fdom']
        mag_voice_slope = descriptors['mag_voice_slope']
        denominator = hfc_normalized * (norm_fdom**fdom_exponent + epsilon)
        with np.errstate(divide='ignore', invalid='ignore'):
            woss = np.where(denominator > epsilon, mag_voice_slope / denominator, 0.0)
            woss[~np.isfinite(woss)] = 0.0
            woss = np.clip(woss, -1e6, 1e6)
        fig.add_trace(go.Scatter(x=woss, y=time_vector, mode='lines', line=dict(color='purple')), row=row, col=col)
        # Use the cmap_min/cmap_max values from Step 2 if available
        min_val = plot_settings.get('woss_cmap_min', plot_settings.get('woss_min', -3))
        max_val = plot_settings.get('woss_cmap_max', plot_settings.get('woss_max', 3))
        fig.update_xaxes(title_text='WOSS Value', row=row, col=col, range=[min_val, max_val])
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])

    # Add marker if applicable
    if trace_data_item.get('marker_value') is not None:
        fig.add_hline(y=trace_data_item['marker_value'], line=dict(color='red', dash='dash'), row=row, col=col)